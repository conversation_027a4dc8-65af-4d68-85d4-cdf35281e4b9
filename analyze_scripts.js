const fs = require('fs');

// Read the tampermonkey script file
const data = fs.readFileSync('tpmonkey script.txt', 'utf8');
const scriptCollection = JSON.parse(data);

console.log('=== TAMPERMONKEY SCRIPT COLLECTION ANALYSIS ===\n');
console.log(`Total scripts found: ${scriptCollection.scripts.length}\n`);

scriptCollection.scripts.forEach((script, index) => {
    console.log(`${index + 1}. Script Name: ${script.name}`);
    console.log(`   Enabled: ${script.enabled}`);
    console.log(`   Position: ${script.position}`);
    
    // Decode base64 source code
    const sourceCode = Buffer.from(script.source, 'base64').toString('utf8');
    
    // Extract key information from the source code
    const lines = sourceCode.split('\n');
    const userScriptHeader = lines.filter(line => line.includes('@'));
    
    console.log('   Script Details:');
    userScriptHeader.forEach(line => {
        if (line.includes('@name') || line.includes('@description') || line.includes('@match') || line.includes('@version')) {
            console.log(`     ${line.trim()}`);
        }
    });
    
    // Extract main functionality
    const mainCode = lines.filter(line => !line.startsWith('//') && line.trim() !== '').slice(0, 10);
    console.log('   Main functionality preview:');
    mainCode.slice(0, 3).forEach(line => {
        if (line.trim()) {
            console.log(`     ${line.trim()}`);
        }
    });
    
    console.log('\n' + '='.repeat(80) + '\n');
});
