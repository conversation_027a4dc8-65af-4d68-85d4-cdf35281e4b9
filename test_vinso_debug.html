<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VinSolutions Debug Test - VinConnect</title>
    <link rel="stylesheet" href="styles/content.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .highlightpanel {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            background: #fafafa;
            cursor: pointer;
        }
        .CustomerInfo_CustomerName {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
        .CustomerInfo_CustomerDetail {
            font-size: 14px;
            line-height: 1.4;
            color: #666;
        }
        #customer-email-span {
            color: #0066cc;
        }
        .debug-section {
            background: #e8f4fd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VinSolutions VinConnect Debug Test</h1>
        <p><strong>URL:</strong> https://vinsolutions.app.coxautoinc.com/vinconnect</p>
        <p>This page simulates the exact VinSolutions VinConnect structure to test our Click to Call functionality.</p>
        
        <div class="debug-section">
            <h3>Debug Information</h3>
            <p>Open browser console (F12) to see detailed logging from the VinSolutions Click to Call script.</p>
            <button onclick="window.vinSoClickToCall && window.vinSoClickToCall.convertToDialpadLinks()"
                    style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                Manual Rescan
            </button>
            <button onclick="location.reload()"
                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                Reload Page
            </button>
        </div>

        <!-- Exact structure from your HTML -->
        <div id="ContentPlaceHolder1_m_CustomerAndTaskInfo_m_CustomerInfo_m_div_EditClick" onclick="OpenCustomerEdit(1325354875);" class="highlightpanel">
            <span id="ContentPlaceHolder1_m_CustomerAndTaskInfo_m_CustomerInfo__CustomerName" class="CustomerInfo_CustomerName" style="font-weight:bold;">Alex Perez</span><br>
            <span id="ContentPlaceHolder1_m_CustomerAndTaskInfo_m_CustomerInfo__CustomerDetail" class="CustomerInfo_CustomerDetail">(Individual)<br>H: (*************<br>C: (*************<br><br><span><span id="customer-email-span" data-emailvalid="true" data-email="<EMAIL>"><EMAIL></span><span id="customer-email2-span" data-emailvalid="false" data-email="" style="display: none;"></span><span style="display:none" id="customer-email-validation-warning"><img title="Email address is invalid, please update" src="../../images/warningsmall.gif"></span></span><br>513 E County Road 138<br>Midland, TX, 79706-7058<br></span>
        </div>

        <!-- Additional test cases -->
        <div class="highlightpanel">
            <span class="CustomerInfo_CustomerName">Maria Rodriguez</span><br>
            <span class="CustomerInfo_CustomerDetail">(Individual)<br>H: (*************<br>C: (*************<br>W: (*************<br><EMAIL><br>123 Main Street<br>El Paso, TX, 79901<br></span>
        </div>

        <div class="highlightpanel">
            <span class="CustomerInfo_CustomerName">John Smith</span><br>
            <span class="CustomerInfo_CustomerDetail">(Individual)<br>Home: (*************<br>Cell: (*************<br>Office: (*************<br><EMAIL><br>456 Oak Avenue<br>Midland, TX, 79705<br></span>
        </div>

        <!-- Test with different formats -->
        <div class="test-section" style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h3>Additional Phone Number Formats</h3>
            <p>Standard format: (*************</p>
            <p>Ten digits: 1234567890</p>
            <p>With dashes: ************</p>
            <p>VinSo H format: H: (*************</p>
            <p>VinSo C format: C: (*************</p>
            <p>Work format: W: (*************</p>
        </div>
    </div>

    <!-- Load our VinSolutions Click to Call script for testing -->
    <script src="scripts/utils.js"></script>
    <script>
        // Simulate VinSolutions VinConnect URL for testing
        Object.defineProperty(window.location, 'href', {
            writable: true,
            value: 'https://vinsolutions.app.coxautoinc.com/vinconnect'
        });
        
        console.log('[Test] Simulated URL set to:', window.location.href);
        
        // Initialize TrueBDCUtils for testing
        if (typeof TrueBDCUtils === 'undefined') {
            console.log('[Test] Creating mock TrueBDCUtils...');
            window.TrueBDCUtils = {
                log: (msg, data) => console.log('[TrueBDC]', msg, data),
                error: (msg, error) => console.error('[TrueBDC]', msg, error),
                logActivity: (action, data) => console.log('[Activity]', action, data),
                createElement: (tag, attrs, styles) => {
                    const el = document.createElement(tag);
                    if (attrs) Object.keys(attrs).forEach(attr => el.setAttribute(attr, attrs[attr]));
                    if (styles) Object.keys(styles).forEach(style => el.style[style] = styles[style]);
                    return el;
                },
                formatPhoneNumber: (number) => {
                    const cleaned = number.replace(/\D/g, '');
                    return `(${cleaned.slice(0,3)}) ${cleaned.slice(3,6)}-${cleaned.slice(6)}`;
                },
                getFrameContext: () => ({ isIframe: false, isMainFrame: true }),
                getScriptState: (script) => Promise.resolve(true),
                detectCRMSystem: () => 'VinSolutions'
            };
            console.log('[Test] Mock TrueBDCUtils created');
        }
        
        // Mock OpenCustomerEdit function
        window.OpenCustomerEdit = function(id) {
            console.log('[Test] OpenCustomerEdit called with ID:', id);
        };
    </script>
    <script src="scripts/vinso-click-to-call.js"></script>
    <script>
        // Additional debugging
        document.addEventListener('DOMContentLoaded', () => {
            console.log('[Test] DOM loaded, checking VinSo Click to Call...');
            
            setTimeout(() => {
                console.log('[Test] Checking if VinSo Click to Call is active...');
                if (window.vinSoClickToCall) {
                    console.log('[Test] VinSo Click to Call instance found:', window.vinSoClickToCall);
                    console.log('[Test] Is active:', window.vinSoClickToCall.isActive);
                } else {
                    console.log('[Test] VinSo Click to Call instance not found');
                }
                
                // Check for phone icons
                const icons = document.querySelectorAll('.truebdc-vinso-phone-icon');
                console.log('[Test] Phone icons found:', icons.length);
                icons.forEach((icon, index) => {
                    console.log('[Test] Icon', index, ':', icon);
                });
            }, 2000);
        });
    </script>
</body>
</html>
