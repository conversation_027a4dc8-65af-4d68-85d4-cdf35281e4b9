// TrueBDC CRM Automation Suite - VinSolutions Click to Call
// Based on the working Tampermonkey script for VinSolutions CRM

class VinSoClickToCall {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.lastClickedNumber = null;
        this.lastClickTime = 0;
        this.clickThreshold = 1000 * 60 * 5; // 5 minutes threshold for consecutive clicks
        this.lastHoveredIcon = null; // For clipboard copy functionality
        this.observer = null;
        this.scanTimeout = null;
        this.scanInterval = null;

        this.init();
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            TrueBDCUtils.log('Initializing VinSolutions Click to Call', {
                frameContext: frameContext
            });

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupPhoneDetection();
                this.isActive = true;

                TrueBDCUtils.log('VinSolutions Click to Call activated', {
                    frameContext: frameContext
                });
                TrueBDCUtils.logActivity('vinso_click_to_call_activated', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('VinSolutions Click to Call not activated - unsupported page', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize VinSolutions Click to Call', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            /vinsolutions\.app\.coxautoinc\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i,
            /apps\.vinmanager\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i
        ];

        const isSupported = supportedPatterns.some(pattern => pattern.test(url));
        TrueBDCUtils.log('VinSolutions Click to Call URL check', {
            url: url,
            isSupported: isSupported
        });
        return isSupported;
    }

    setupPhoneDetection() {
        // Initial scan for phone numbers
        this.convertToDialpadLinks();

        // Set up mutation observer to detect new phone numbers
        this.setupMutationObserver();

        // Periodic scan as fallback
        this.periodicallyConvertToDialpadLinks();
    }

    setupMutationObserver() {
        const config = { childList: true, subtree: true };
        const callback = (mutationsList, observer) => {
            for (let mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    this.convertToDialpadLinks();
                }
            }
        };

        // Target the deal-details container or fallback to document.body
        const targetNode = document.querySelector('.deal-details') || document.body;
        if (targetNode) {
            this.observer = new MutationObserver(callback);
            this.observer.observe(targetNode, config);
            TrueBDCUtils.log('VinSolutions mutation observer started');
        } else {
            TrueBDCUtils.log('Deal details container not found, retrying...');
            setTimeout(() => this.setupMutationObserver(), 3000);
        }
    }

    periodicallyConvertToDialpadLinks() {
        this.scanInterval = setInterval(() => {
            this.convertToDialpadLinks();
        }, 3000); // Run every 3 seconds

        // Stop after 3 minutes to prevent indefinite execution
        setTimeout(() => {
            if (this.scanInterval) {
                clearInterval(this.scanInterval);
                this.scanInterval = null;
            }
        }, 180000);
    }

    convertToDialpadLinks() {
        try {
            // Target VinSolutions specific phone number elements
            document.querySelectorAll('span[analyticsdetect="DealCard|Navigate|Phone"].phone-detail').forEach(span => {
                const originalPhoneNumber = span.textContent.trim();
                if (originalPhoneNumber && !span.dataset.converted) {
                    // Mark the span as processed to avoid re-processing
                    span.dataset.converted = true;
                    this.insertClickableIcon(span, originalPhoneNumber, originalPhoneNumber);
                }
            });

            // Also process general text nodes for phone numbers
            this.findAndProcessTextNodes();
        } catch (error) {
            TrueBDCUtils.error('Error in convertToDialpadLinks', error);
        }
    }

    findAndProcessTextNodes() {
        const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, null, false);
        let nodes = [];
        while (walker.nextNode()) {
            nodes.push(walker.currentNode);
        }
        nodes.forEach(node => {
            if (/(\(\d{3}\)\s?\d{3}-\d{4})|(\b\d{10}\b)/.test(node.nodeValue)) {
                this.processTextNode(node);
            }
        });
    }

    processTextNode(node) {
        const phoneRegEx = /(\(\d{3}\)\s?\d{3}-\d{4})|(\b\d{10}\b)/g;
        let match;
        while ((match = phoneRegEx.exec(node.textContent)) !== null) {
            this.insertClickableIcon(node, match[0], match[0]);
        }
    }