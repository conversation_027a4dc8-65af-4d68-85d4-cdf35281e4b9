// TrueBDC CRM Automation Suite - VinSolutions Click to Call
// Based on the original Tampermonkey script for VinSolutions

class VinSoClickToCall {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.lastClickedNumber = null;
        this.lastClickTime = 0;
        this.clickThreshold = 1000 * 60 * 5; // 5 minutes threshold for consecutive clicks
        this.lastHoveredIcon = null; // For clipboard copy functionality
        this.observer = null;
        this.periodicInterval = null;
        
        this.init();
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            TrueBDCUtils.log('Initializing VinSolutions Click to Call', {
                frameContext: frameContext
            });

            // Check if we're on a supported VinSolutions page
            if (this.isSupportedPage()) {
                this.setupClickToCall();
                this.isActive = true;

                TrueBDCUtils.log('VinSolutions Click to Call activated', {
                    frameContext: frameContext
                });
                TrueBDCUtils.logActivity('vinso_click_to_call_activated', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('VinSolutions Click to Call not activated - unsupported page', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize VinSolutions Click to Call', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        console.log('[VinSo Click to Call] Checking URL:', url);

        const supportedPatterns = [
            /vinsolutions\.app\.coxautoinc\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i,
            /vinsolutions\.app\.coxautoinc\.com\/vinconnect/i, // New pattern you provided
            /apps\.vinmanager\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i,
            /vinsolutions\.com/i // Legacy pattern
        ];

        const isSupported = supportedPatterns.some(pattern => {
            const matches = pattern.test(url);
            console.log('[VinSo Click to Call] Pattern test:', pattern.toString(), 'matches:', matches);
            return matches;
        });

        console.log('[VinSo Click to Call] Page supported:', isSupported);
        if (isSupported) {
            TrueBDCUtils.log('VinSolutions Click to Call detected supported page', { url });
        } else {
            console.log('[VinSo Click to Call] Page not supported for URL:', url);
        }
        return isSupported;
    }

    setupClickToCall() {
        console.log('[VinSo Click to Call] Setting up click to call functionality...');

        // Initial conversion
        console.log('[VinSo Click to Call] Running initial conversion...');
        this.convertToDialpadLinks();
        this.findAndProcessTextNodes();

        // Set up mutation observer for dynamic content
        console.log('[VinSo Click to Call] Setting up mutation observer...');
        this.setupMutationObserver();

        // Set up periodic scanning (every 3 seconds like original)
        console.log('[VinSo Click to Call] Setting up periodic scanning...');
        this.setupPeriodicScanning();

        console.log('[VinSo Click to Call] Setup complete');
    }

    setupMutationObserver() {
        const config = { childList: true, subtree: true };
        const callback = (mutationsList, observer) => {
            for (let mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    this.convertToDialpadLinks();
                }
            }
        };

        // Try to find a stable container, fallback to document.body
        const targetNode = document.querySelector('.deal-details') || 
                          document.querySelector('#buyerColumn') || 
                          document.body;
        
        if (targetNode) {
            this.observer = new MutationObserver(callback);
            this.observer.observe(targetNode, config);
            TrueBDCUtils.log('VinSolutions mutation observer set up', { target: targetNode.tagName });
        }
    }

    setupPeriodicScanning() {
        // Periodic scanning every 3 seconds (like original script)
        this.periodicInterval = setInterval(() => {
            this.convertToDialpadLinks();
            this.findAndProcessTextNodes();
        }, 3000);

        // Stop after 3 minutes to prevent indefinite execution
        setTimeout(() => {
            if (this.periodicInterval) {
                clearInterval(this.periodicInterval);
                this.periodicInterval = null;
                TrueBDCUtils.log('VinSolutions periodic scanning stopped');
            }
        }, 180000);
    }

    convertToDialpadLinks() {
        console.log('[VinSo Click to Call] Starting convertToDialpadLinks...');

        // Handle specific VinSolutions elements with phone numbers
        const selectors = [
            'span[analyticsdetect="DealCard|Navigate|Phone"].phone-detail',
            '#buyerColumn .standardText',
            '#cobuyer .standardText',
            '.customerName + .standardText',
            '[id*="BuyerInfoDetails"]',
            '[id*="CobuyerInfoDetails"]',
            // New selectors based on your HTML structure
            '.CustomerInfo_CustomerDetail',
            '[id*="CustomerDetail"]',
            '[class*="CustomerInfo"]'
        ];

        selectors.forEach(selector => {
            console.log('[VinSo Click to Call] Checking selector:', selector);
            const elements = document.querySelectorAll(selector);
            console.log('[VinSo Click to Call] Found elements for selector', selector, ':', elements.length);

            elements.forEach((element, index) => {
                console.log('[VinSo Click to Call] Processing element', index, 'for selector', selector);
                console.log('[VinSo Click to Call] Element text:', element.textContent.trim());

                if (!element.dataset.converted) {
                    const text = element.textContent.trim();
                    if (text && this.containsPhoneNumber(text)) {
                        console.log('[VinSo Click to Call] Phone number detected in element:', text);
                        element.dataset.converted = true;
                        this.processElementForPhoneNumbers(element);
                    } else {
                        console.log('[VinSo Click to Call] No phone number detected in element:', text);
                    }
                } else {
                    console.log('[VinSo Click to Call] Element already converted');
                }
            });
        });
    }

    findAndProcessTextNodes() {
        const walker = document.createTreeWalker(
            document.body, 
            NodeFilter.SHOW_TEXT, 
            null, 
            false
        );
        
        const nodes = [];
        while (walker.nextNode()) {
            nodes.push(walker.currentNode);
        }
        
        nodes.forEach(node => {
            if (this.containsPhoneNumber(node.nodeValue)) {
                this.processTextNode(node);
            }
        });
    }

    containsPhoneNumber(text) {
        console.log('[VinSo Click to Call] Checking text for phone numbers:', text);

        const phonePatterns = [
            /(\(\d{3}\)\s?\d{3}-\d{4})/g,  // (*************
            /(\b\d{10}\b)/g,               // 1234567890
            /(Eve|Cell|Home|Work|Phone|Mobile|Office):\s*\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/gi,
            // New patterns based on your HTML structure
            /(H|C):\s*\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/gi, // H: (*************, C: (*************
            /\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g // General phone pattern
        ];

        const hasPhone = phonePatterns.some(pattern => {
            const matches = pattern.test(text);
            if (matches) {
                console.log('[VinSo Click to Call] Pattern matched:', pattern.toString());
            }
            return matches;
        });

        console.log('[VinSo Click to Call] Contains phone number:', hasPhone);
        return hasPhone;
    }

    processElementForPhoneNumbers(element) {
        const text = element.textContent;
        console.log('[VinSo Click to Call] Processing element for phone numbers:', text);

        const phonePatterns = [
            /(\(\d{3}\)\s?\d{3}-\d{4})/g,
            /(\b\d{10}\b)/g,
            /(Eve|Cell|Home|Work|Phone|Mobile|Office):\s*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/gi,
            // New patterns for H: and C: format
            /(H|C):\s*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/gi
        ];

        phonePatterns.forEach((pattern, patternIndex) => {
            console.log('[VinSo Click to Call] Testing pattern', patternIndex, ':', pattern.toString());
            const matches = text.match(pattern);
            console.log('[VinSo Click to Call] Pattern matches:', matches);

            if (matches) {
                matches.forEach((match, matchIndex) => {
                    console.log('[VinSo Click to Call] Processing match', matchIndex, ':', match);
                    // Extract just the phone number part
                    const phoneMatch = match.match(/\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/);
                    console.log('[VinSo Click to Call] Extracted phone number:', phoneMatch);

                    if (phoneMatch) {
                        console.log('[VinSo Click to Call] Adding icon for phone:', phoneMatch[0], 'original:', match);
                        this.insertClickableIcon(element, phoneMatch[0], match);
                    }
                });
            }
        });
    }

    processTextNode(node) {
        const phoneRegEx = /(\(\d{3}\)\s?\d{3}-\d{4})|(\b\d{10}\b)/g;
        let match;
        while ((match = phoneRegEx.exec(node.textContent)) !== null) {
            this.insertClickableIcon(node, match[0], match[0]);
        }
    }

    insertClickableIcon(node, phoneNumber, originalFormat) {
        const cleanNumber = phoneNumber.replace(/\D/g, '');
        
        // Check if icon already exists
        const iconId = `vinso-phone-icon-${cleanNumber}-${Math.random().toString(36).substr(2, 9)}`;
        if (node.querySelector && node.querySelector(`[data-phone="${cleanNumber}"]`)) {
            return;
        }

        const icon = TrueBDCUtils.createElement('a', {
            id: iconId,
            href: `callto:+1${cleanNumber}`,
            title: 'Click to call',
            'data-phone': cleanNumber,
            'data-original-number': originalFormat,
            class: 'truebdc-vinso-phone-icon'
        });

        // Use the same base64 icon as the original script
        icon.innerHTML = `<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAEzlAABM5QF1zvCVAAAgAElEQVR4nO29d5xdR303/P3NzDnn1u1Vu9pVb7YlS3JvYMdgDC60B3AoIYTiACEvISFvkpfwCYEHEp6HQMD0hICxscFAbDDGxtiWe5XVe9f2vbt799ZTZub3/nHurpolS7Zsy/nw/fjoeu89Z87M/Kb8+hAz4w84dSBe6Qr8AYfiDwQ5xfAHgpxi+ANBTjGoV+rFPP0PADr8h9p3/ByfUzcRAcwgokMf4fjm2s9Hln3w34c8d9BvrxCfQwTQq4TLooMvjunBONB1fPi9NVqBAY4/Drn/lIUCAGsZxtqX9cXaWDADjLjnpkY0M08NZMGAqH1tJBELQSAiWMvwHIlCJUDKc+AoCQCwzChUAk57LogAbS1E7X5mhmUWiJdpKwQxGGyZIYWANhZKCYCByJiXtS+YGa6jkHBUTJD+sQKe3t4Pz1F4qWcMM8NVEo9s2o/cZAVF30dTXRrtdWmMFspU9i" alt="Call Icon" width="14" height="14">`;

        // Add styling
        icon.style.cursor = 'pointer';
        icon.style.marginLeft = '5px';
        icon.style.textDecoration = 'none';
        icon.style.color = 'inherit';

        // Add click handler
        icon.addEventListener('click', (e) => {
            e.stopPropagation();
            if (!this.updateClickCount(originalFormat, e)) {
                e.preventDefault();
                TrueBDCUtils.log("VinSolutions dialing canceled by user");
            } else {
                TrueBDCUtils.log(`VinSolutions proceeding to dial: callto:+1${cleanNumber}`);
                TrueBDCUtils.logActivity('vinso_phone_call_initiated', {
                    phoneNumber: cleanNumber,
                    originalFormat: originalFormat,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Add copy functionality
        icon.addEventListener('mouseover', () => {
            this.lastHoveredIcon = icon;
            document.addEventListener('keydown', this.handleCopy.bind(this));
        });

        icon.addEventListener('mouseout', () => {
            document.removeEventListener('keydown', this.handleCopy.bind(this));
            this.lastHoveredIcon = null;
        });

        // Insert the icon
        if (node.nodeType === Node.TEXT_NODE) {
            node.parentNode.insertBefore(icon, node.nextSibling);
        } else {
            node.appendChild(icon);
        }

        TrueBDCUtils.log('VinSolutions click-to-call icon added', {
            phoneNumber: cleanNumber,
            originalFormat: originalFormat
        });
    }

    updateClickCount(phoneNumber, event) {
        const now = Date.now();
        const countKey = `vinso_dialCount_${phoneNumber}`;
        let currentCount = parseInt(localStorage.getItem(countKey), 10) || 0;

        if (phoneNumber !== this.lastClickedNumber || (now - this.lastClickTime) > this.clickThreshold) {
            currentCount = 1;
        } else {
            if (currentCount === 2) {
                const confirmDial = confirm(`You're about to dial this number for the 3rd time in a row, are you sure you want to dial once more?`);
                if (!confirmDial) {
                    this.showTooltip(`Dialing canceled for ${phoneNumber}.`, event.clientX, event.clientY);
                    return false;
                }
            }
            currentCount++;
        }

        localStorage.setItem(countKey, currentCount);
        this.lastClickedNumber = phoneNumber;
        this.lastClickTime = now;

        this.showTooltip(`You have dialed ${phoneNumber} ${currentCount} time(s).`, event.clientX, event.clientY);
        return true;
    }

    showTooltip(text, x, y) {
        let tooltip = document.getElementById('vinso-dialpad-tooltip');
        if (!tooltip) {
            tooltip = TrueBDCUtils.createElement('div', {
                id: 'vinso-dialpad-tooltip'
            });
            tooltip.style.position = 'absolute';
            tooltip.style.background = 'white';
            tooltip.style.color = 'black';
            tooltip.style.padding = '5px 10px';
            tooltip.style.borderRadius = '4px';
            tooltip.style.fontSize = '12px';
            tooltip.style.display = 'none';
            tooltip.style.zIndex = '1000';
            tooltip.style.border = '1px solid blue';
            document.body.appendChild(tooltip);
        }

        tooltip.innerText = text;
        tooltip.style.left = `${x}px`;
        tooltip.style.top = `${y}px`;
        tooltip.style.display = 'block';

        setTimeout(() => {
            tooltip.style.display = 'none';
        }, 3000);
    }

    handleCopy(e) {
        if (this.lastHoveredIcon && e.ctrlKey && (e.key === 'c' || e.key === 'C')) {
            e.preventDefault();
            const originalFormat = this.lastHoveredIcon.getAttribute('data-original-number');
            navigator.clipboard.writeText(originalFormat).then(() => {
                const rect = this.lastHoveredIcon.getBoundingClientRect();
                const x = rect.left + window.scrollX + (rect.width / 2);
                const y = rect.top + window.scrollY;
                this.showTooltip('Phone Number Copied to Clipboard', x, y);

                TrueBDCUtils.logActivity('vinso_phone_copied', {
                    phoneNumber: originalFormat,
                    timestamp: new Date().toISOString()
                });

                setTimeout(() => {
                    let tooltip = document.getElementById('vinso-dialpad-tooltip');
                    if (tooltip) tooltip.style.display = 'none';
                }, 2000);
            }).catch(err => {
                TrueBDCUtils.error('Error copying text to clipboard:', err);
            });
        }
    }

    destroy() {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }

        if (this.periodicInterval) {
            clearInterval(this.periodicInterval);
            this.periodicInterval = null;
        }

        // Remove all added icons
        document.querySelectorAll('.truebdc-vinso-phone-icon').forEach(icon => {
            icon.remove();
        });

        // Remove tooltip
        const tooltip = document.getElementById('vinso-dialpad-tooltip');
        if (tooltip) {
            tooltip.remove();
        }

        this.isActive = false;
        TrueBDCUtils.log('VinSolutions Click to Call destroyed');
    }
}

// Auto-initialize if TrueBDCUtils is available
if (typeof TrueBDCUtils !== 'undefined') {
    console.log('[VinSo Click to Call] TrueBDCUtils available, initializing...');
    // Initialize VinSolutions Click to Call
    window.vinSoClickToCall = new VinSoClickToCall();
} else {
    console.log('[VinSo Click to Call] TrueBDCUtils not available, waiting...');
    // Wait for TrueBDCUtils to be available
    const checkForUtils = setInterval(() => {
        if (typeof TrueBDCUtils !== 'undefined') {
            console.log('[VinSo Click to Call] TrueBDCUtils now available, initializing...');
            clearInterval(checkForUtils);
            window.vinSoClickToCall = new VinSoClickToCall();
        }
    }, 100);

    // Stop checking after 10 seconds
    setTimeout(() => {
        clearInterval(checkForUtils);
        console.log('[VinSo Click to Call] Timeout waiting for TrueBDCUtils');
    }, 10000);
}
