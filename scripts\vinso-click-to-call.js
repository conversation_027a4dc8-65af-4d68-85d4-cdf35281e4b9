// TrueBDC CRM Automation Suite - VinSolutions Click to Call
// Based on the working Tampermonkey script for VinSolutions CRM

class VinSoClickToCall {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.lastClickedNumber = null;
        this.lastClickTime = 0;
        this.clickThreshold = 1000 * 60 * 5; // 5 minutes threshold for consecutive clicks
        this.lastHoveredIcon = null; // For clipboard copy functionality
        this.observer = null;
        this.scanTimeout = null;
        this.scanInterval = null;

        this.init();
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            TrueBDCUtils.log('Initializing VinSolutions Click to Call', {
                frameContext: frameContext
            });

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupPhoneDetection();
                this.isActive = true;

                TrueBDCUtils.log('VinSolutions Click to Call activated', {
                    frameContext: frameContext
                });
                TrueBDCUtils.logActivity('vinso_click_to_call_activated', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('VinSolutions Click to Call not activated - unsupported page', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize VinSolutions Click to Call', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            /vinsolutions\.app\.coxautoinc\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i,
            /apps\.vinmanager\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i
        ];

        const isSupported = supportedPatterns.some(pattern => pattern.test(url));
        TrueBDCUtils.log('VinSolutions Click to Call URL check', {
            url: url,
            isSupported: isSupported
        });
        return isSupported;
    }

    setupPhoneDetection() {
        // Initial scan for phone numbers
        this.convertToDialpadLinks();

        // Set up mutation observer to detect new phone numbers
        this.setupMutationObserver();

        // Periodic scan as fallback
        this.periodicallyConvertToDialpadLinks();
    }

    setupMutationObserver() {
        const config = { childList: true, subtree: true };
        const callback = (mutationsList, observer) => {
            for (let mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    this.convertToDialpadLinks();
                }
            }
        };

        // Target the deal-details container or fallback to document.body
        const targetNode = document.querySelector('.deal-details') || document.body;
        if (targetNode) {
            this.observer = new MutationObserver(callback);
            this.observer.observe(targetNode, config);
            TrueBDCUtils.log('VinSolutions mutation observer started');
        } else {
            TrueBDCUtils.log('Deal details container not found, retrying...');
            setTimeout(() => this.setupMutationObserver(), 3000);
        }
    }

    periodicallyConvertToDialpadLinks() {
        this.scanInterval = setInterval(() => {
            this.convertToDialpadLinks();
        }, 3000); // Run every 3 seconds

        // Stop after 3 minutes to prevent indefinite execution
        setTimeout(() => {
            if (this.scanInterval) {
                clearInterval(this.scanInterval);
                this.scanInterval = null;
            }
        }, 180000);
    }

    convertToDialpadLinks() {
        try {
            // Target VinSolutions specific phone number elements
            document.querySelectorAll('span[analyticsdetect="DealCard|Navigate|Phone"].phone-detail').forEach(span => {
                const originalPhoneNumber = span.textContent.trim();
                if (originalPhoneNumber && !span.dataset.converted) {
                    // Mark the span as processed to avoid re-processing
                    span.dataset.converted = true;
                    this.insertClickableIcon(span, originalPhoneNumber, originalPhoneNumber);
                }
            });

            // Also process general text nodes for phone numbers
            this.findAndProcessTextNodes();
        } catch (error) {
            TrueBDCUtils.error('Error in convertToDialpadLinks', error);
        }
    }

    findAndProcessTextNodes() {
        const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, null, false);
        let nodes = [];
        while (walker.nextNode()) {
            nodes.push(walker.currentNode);
        }
        nodes.forEach(node => {
            if (/(\(\d{3}\)\s?\d{3}-\d{4})|(\b\d{10}\b)/.test(node.nodeValue)) {
                this.processTextNode(node);
            }
        });
    }

    processTextNode(node) {
        const phoneRegEx = /(\(\d{3}\)\s?\d{3}-\d{4})|(\b\d{10}\b)/g;
        let match;
        while ((match = phoneRegEx.exec(node.textContent)) !== null) {
            this.insertClickableIcon(node, match[0], match[0]);
        }
    }

    insertClickableIcon(node, phoneNumber, originalFormat) {
        const cleanNumber = phoneNumber.replace(/\D/g, '');

        // Check if icon already exists for this number
        const iconId = `vinso-phone-icon-${cleanNumber}`;
        if (document.getElementById(iconId)) return;

        const icon = TrueBDCUtils.createElement('a', {
            id: iconId,
            href: `callto:+1${cleanNumber}`,
            title: 'Click to call',
            'data-phone': cleanNumber,
            'data-original-number': originalFormat,
            class: 'truebdc-vinso-phone-icon'
        });

        // SVG ICON PLACEHOLDER - PASTE THE EXISTING SVG HERE
            icon.innerHTML = `<img src="data:image/png;base64,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" alt="Call Icon" width="14" height="14">`;
            <!-- PASTE THE EXISTING CLICK-TO-CALL SVG ICON HERE -->
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M8 12l2 2 4-4" stroke="currentColor" stroke-width="2"/>
        </svg>`;

        // Add click handler
        icon.addEventListener('click', (e) => {
            e.stopPropagation();
            if (!this.updateClickCount(originalFormat, e)) {
                e.preventDefault();
                console.log("Dialing canceled by user.");
            } else {
                console.log(`Proceeding to dial: callto:+1${cleanNumber}`);
            }
        });

        // Add hover handlers for copy functionality
        icon.addEventListener('mouseover', () => {
            this.lastHoveredIcon = icon;
            document.addEventListener('keydown', this.handleCopy.bind(this));
        });

        icon.addEventListener('mouseout', () => {
            document.removeEventListener('keydown', this.handleCopy.bind(this));
            this.lastHoveredIcon = null;
        });

        // Insert icon after the node
        if (node.parentNode) {
            node.parentNode.insertBefore(icon, node.nextSibling);
        }
    }

    updateClickCount(phoneNumber, event) {
        const now = Date.now();
        const countKey = `vinso_dialCount_${phoneNumber}`;
        let currentCount = parseInt(localStorage.getItem(countKey), 10) || 0;

        if (phoneNumber !== this.lastClickedNumber || (now - this.lastClickTime) > this.clickThreshold) {
            currentCount = 1;
        } else {
            if (currentCount === 2) {
                const confirmDial = confirm(`You're about to dial this number for the 3rd time in a row, are you sure you want to dial once more?`);
                if (!confirmDial) {
                    this.showTooltip(`Dialing canceled for ${phoneNumber}.`, event.clientX, event.clientY);
                    return false;
                }
            }
            currentCount++;
        }

        localStorage.setItem(countKey, currentCount);
        this.lastClickedNumber = phoneNumber;
        this.lastClickTime = now;

        this.showTooltip(`You have dialed ${phoneNumber} ${currentCount} time(s).`, event.clientX, event.clientY);

        // Log activity
        TrueBDCUtils.logActivity('vinso_phone_call_initiated', {
            phoneNumber: phoneNumber,
            count: currentCount,
            timestamp: new Date().toISOString()
        });

        return true;
    }

    showTooltip(text, x, y) {
        let tooltip = document.getElementById('vinso-dialpad-tooltip');
        if (!tooltip) {
            tooltip = TrueBDCUtils.createElement('div', {
                id: 'vinso-dialpad-tooltip'
            }, {
                position: 'absolute',
                background: 'white',
                color: 'black',
                padding: '5px 10px',
                borderRadius: '4px',
                fontSize: '12px',
                display: 'none',
                zIndex: '1000',
                border: '1px solid blue'
            });
            document.body.appendChild(tooltip);
        }

        tooltip.innerText = text;
        tooltip.style.left = `${x}px`;
        tooltip.style.top = `${y}px`;
        tooltip.style.display = 'block';

        setTimeout(() => {
            tooltip.style.display = 'none';
        }, 3000);
    }

    handleCopy(e) {
        if (this.lastHoveredIcon && e.ctrlKey && (e.key === 'c' || e.key === 'C')) {
            e.preventDefault();
            const originalFormat = this.lastHoveredIcon.getAttribute('data-original-number');
            navigator.clipboard.writeText(originalFormat).then(() => {
                const rect = this.lastHoveredIcon.getBoundingClientRect();
                const x = rect.left + window.scrollX + (rect.width / 2);
                const y = rect.top + window.scrollY;
                this.showTooltip('Phone Number Copied to Clipboard', x, y);
                setTimeout(() => {
                    let tooltip = document.getElementById('vinso-dialpad-tooltip');
                    if (tooltip) tooltip.style.display = 'none';
                }, 2000);
            }).catch(err => console.error('Error copying text to clipboard:', err));
        }
    }

    destroy() {
        try {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            if (this.scanInterval) {
                clearInterval(this.scanInterval);
                this.scanInterval = null;
            }

            if (this.scanTimeout) {
                clearTimeout(this.scanTimeout);
                this.scanTimeout = null;
            }

            // Remove event listeners
            document.removeEventListener('keydown', this.handleCopy.bind(this));

            this.isActive = false;
            TrueBDCUtils.log('VinSolutions Click to Call destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying VinSolutions Click to Call', error);
        }
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasObserver: !!this.observer,
            hasScanInterval: !!this.scanInterval
        };
    }
}

// Make class globally available
window.VinSoClickToCall = VinSoClickToCall;