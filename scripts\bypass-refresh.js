// TrueBDC CRM Automation Suite - Bypass Refresh Confirmation

class BypassRefresh {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.beforeUnloadHandler = null;
        
        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Bypass Refresh Confirmation');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupKeyListener();
                this.setupBeforeUnloadHandler();
                this.isActive = true;
                
                TrueBDCUtils.log('Bypass Refresh Confirmation activated');
                TrueBDCUtils.logActivity('bypass_refresh_activated', {
                    url: window.location.href
                });
            } else {
                TrueBDCUtils.log('Bypass Refresh Confirmation not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Bypass Refresh Confirmation', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/weblink\/weblinkToday\.aspx/i,
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/reports/i,
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/NewProspects/i,
            /vinsolutions\.com/i // Support VinSolutions as well
        ];

        return supportedPatterns.some(pattern => pattern.test(url));
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Check for F5 key (keyCode 116) or Ctrl+R (Ctrl + keyCode 82)
            if (event.keyCode === 116 || (event.ctrlKey && event.keyCode === 82)) {
                TrueBDCUtils.log('Refresh key detected, bypassing confirmation', {
                    key: event.keyCode === 116 ? 'F5' : 'Ctrl+R',
                    keyCode: event.keyCode,
                    ctrlKey: event.ctrlKey
                });

                // Prevent the default behavior
                event.preventDefault();
                event.stopPropagation();

                // Force reload without cache
                this.performRefresh();

                TrueBDCUtils.logActivity('refresh_bypassed', {
                    method: event.keyCode === 116 ? 'F5' : 'Ctrl+R',
                    timestamp: new Date().toISOString()
                });

                return false;
            }
        };

        // Add event listener with capture to ensure we catch it first
        window.addEventListener('keydown', this.keydownHandler, true);
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    setupBeforeUnloadHandler() {
        // Override any existing beforeunload handlers that might show confirmation dialogs
        this.beforeUnloadHandler = (event) => {
            // Check if this is a refresh operation
            if (this.isRefreshOperation(event)) {
                TrueBDCUtils.log('Bypassing beforeunload confirmation for refresh');
                
                // Don't show confirmation dialog
                delete event.returnValue;
                return undefined;
            }
        };

        window.addEventListener('beforeunload', this.beforeUnloadHandler, true);

        // Also override the onbeforeunload property
        const originalOnBeforeUnload = window.onbeforeunload;
        window.onbeforeunload = (event) => {
            if (this.isRefreshOperation(event)) {
                return undefined;
            }
            
            // Call original handler for non-refresh operations
            if (originalOnBeforeUnload && typeof originalOnBeforeUnload === 'function') {
                return originalOnBeforeUnload.call(window, event);
            }
        };
    }

    isRefreshOperation(event) {
        // This is a heuristic to detect if the page unload is due to a refresh
        // We can't be 100% certain, but we can make educated guesses
        
        // Check if we recently detected a refresh key
        const now = Date.now();
        if (this.lastRefreshKeyTime && (now - this.lastRefreshKeyTime) < 1000) {
            return true;
        }

        // Check if the navigation type indicates a reload
        if (performance.navigation && performance.navigation.type === 1) {
            return true;
        }

        return false;
    }

    performRefresh() {
        try {
            // Mark that we're performing a refresh
            this.lastRefreshKeyTime = Date.now();
            
            // Show a brief notification
            this.showRefreshNotification();

            // Perform the refresh after a short delay to allow the notification to show
            setTimeout(() => {
                location.reload(true); // Force reload without cache
            }, 100);

        } catch (error) {
            TrueBDCUtils.error('Error performing refresh', error);
            // Fallback to regular refresh
            location.reload();
        }
    }

    showRefreshNotification() {
        // Create a brief notification that the page is refreshing
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-refresh-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <div class="truebdc-spinner" style="width: 16px; height: 16px; border-width: 2px;"></div>
                <span>Refreshing page...</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after a short time
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 2000);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('Bypass Refresh settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                window.removeEventListener('keydown', this.keydownHandler, true);
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            if (this.beforeUnloadHandler) {
                window.removeEventListener('beforeunload', this.beforeUnloadHandler, true);
                this.beforeUnloadHandler = null;
            }

            // Reset onbeforeunload
            window.onbeforeunload = null;

            this.isActive = false;
            
            TrueBDCUtils.log('Bypass Refresh Confirmation destroyed');
            TrueBDCUtils.logActivity('bypass_refresh_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Bypass Refresh Confirmation', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            hasBeforeUnloadHandler: !!this.beforeUnloadHandler
        };
    }
}

// Make class globally available
window.BypassRefresh = BypassRefresh;
