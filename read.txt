{"created_by": "Tam<PERSON>mon<PERSON>", "version": "1", "scripts": [{"name": "Open JavaScript Windows in New Window", "options": {"check_for_updates": false, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/Reports/ResponseTimes.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711217862329}, "storage": {"ts": 1711470419068, "data": {}}, "enabled": true, "position": 1, "uuid": "2163e154-f580-4730-8bcd-a66160d46da8", "source": "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"}, {"name": "Dealership Web Search", "options": {"check_for_updates": false, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711219963961}, "storage": {"ts": 1711470419070, "data": {}}, "enabled": true, "position": 2, "uuid": "c684d49f-751d-4e69-98f4-deb5eb5a2ab3", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBEZWFsZXJzaGlwIFdlYiBTZWFyY2gKLy8gQG5hbWVzcGFjZSAgICBodHRwOi8vdGFtcGVybW9ua2V5Lm5ldC8KLy8gQHZlcnNpb24gICAgICAwLjEKLy8gQGRlc2NyaXB0aW9uICBTZWFyY2ggaW52ZW50b3J5IG9uIEdyYW5idXJ5IE5pc3NhbgovLyBAYXV0aG9yICAgICAgIEFsZXgKLy8gQG1hdGNoICAgICAgICBodHRwczovL3ZpbnNvbHV0aW9ucy5hcHAuY294YXV0b2luYy5jb20vQ2FyRGFzaGJvYXJkL1BhZ2VzL0NSTS9DdXN0b21lckRhc2hib2FyZC5hc3B4KgovLyBAZ3JhbnQgICAgICAgIG5vbmUKLy8gPT0vVXNlclNjcmlwdD09CgooZnVuY3Rpb24oKSB7CiAgICAndXNlIHN0cmljdCc7CgogICAgdmFyIGlucHV0Qm94SHRtbCA9IGAKICAgICAgICA8ZGl2IGlkPSJjdXN0b20tc2VhcmNoIiBzdHlsZT0iZGlzcGxheTogYmxvY2s7IHBvc2l0aW9uOiBmaXhlZDsgei1pbmRleDogMTAwMDAwOyBib3R0b206IDVweDsgcmlnaHQ6IDVweDsgcGFkZGluZzogMTBweDsgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZjlmOTsgYm9yZGVyOiAxcHggc29saWQgI2NjYzsgYm9yZGVyLXJhZGl1czogNXB4OyBmb250LXNpemU6IDkwJTsiPgogICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47IGFsaWduLWl0ZW1zOiBjZW50ZXI7Ij4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImZvbnQtc2l6ZTogMS4wOGVtOyBmb250LXdlaWdodDogYm9sZDsiPlNlYXJjaCBvbiBEZWFsZXJzaGlwIFdlYnNpdGU8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgaWQ9ImN1c3RvbS1zZWFyY2gtY2xvc2UiIHN0eWxlPSJjdXJzb3I6IHBvaW50ZXI7IGNvbG9yOiByZWQ7Ij5YPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IHN0eWxlPSJkaXNwbGF5OiBmbGV4OyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47IGFsaWduLWl0ZW1zOiBjZW50ZXI7IG1hcmdpbi10b3A6IDEwcHg7Ij4KICAgICAgICAgICAgICAgIDxsYWJlbCBmb3I9ImN1c3RvbS1zZWFyY2gtaW5wdXQtbmV3Ij5OZXc6PC9sYWJlbD4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgICAgPGlucHV0IGlkPSJjdXN0b20tc2VhcmNoLWlucHV0LW5ldyIgc3R5bGU9IndpZHRoOiAxMTBweDsgcGFkZGluZzogNHB4OyBtYXJnaW4tbGVmdDogNXB4OyIgdHlwZT0idGV4dCIgcGxhY2Vob2xkZXI9IkVudGVyIHNlYXJjaCB0ZXJtLi4uIj4KICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGlkPSJjdXN0b20tc2VhcmNoLWJ1dHRvbi1uZXciIHN0eWxlPSJtYXJnaW4tbGVmdDogNXB4OyBwYWRkaW5nOiA0cHggNnB4OyBiYWNrZ3JvdW5kLWNvbG9yOiAjNENBRjUwOyBjb2xvcjogd2hpdGU7IGJvcmRlcjogbm9uZTsgYm9yZGVyLXJhZGl1czogM3B4OyI+T0s8L2J1dHRvbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiBzdHlsZT0iZGlzcGxheTogZmxleDsganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOyBhbGlnbi1pdGVtczogY2VudGVyOyBtYXJnaW4tdG9wOiAxMHB4OyI+CiAgICAgICAgICAgICAgICA8bGFiZWwgZm9yPSJjdXN0b20tc2VhcmNoLWlucHV0LXVzZWQiPlVzZWQ6PC9sYWJlbD4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgICAgPGlucHV0IGlkPSJjdXN0b20tc2VhcmNoLWlucHV0LXVzZWQiIHN0eWxlPSJ3aWR0aDogMTEwcHg7IHBhZGRpbmc6IDRweDsgbWFyZ2luLWxlZnQ6IDVweDsiIHR5cGU9InRleHQiIHBsYWNlaG9sZGVyPSJFbnRlciBzZWFyY2ggdGVybS4uLiI+CiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBpZD0iY3VzdG9tLXNlYXJjaC1idXR0b24tdXNlZCIgc3R5bGU9Im1hcmdpbi1sZWZ0OiA1cHg7IHBhZGRpbmc6IDRweCA2cHg7IGJhY2tncm91bmQtY29sb3I6ICM0Q0FGNTA7IGNvbG9yOiB3aGl0ZTsgYm9yZGVyOiBub25lOyBib3JkZXItcmFkaXVzOiAzcHg7Ij5PSzwvYnV0dG9uPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgYDsKCiAgICAvLyBDcmVhdGUgYSBuZXcgZGl2IGVsZW1lbnQgdG8gd3JhcCB5b3VyIGN1c3RvbSBIVE1MCiAgICB2YXIgY3VzdG9tU2VhcmNoRGl2ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7CiAgICBjdXN0b21TZWFyY2hEaXYuaW5uZXJIVE1MID0gaW5wdXRCb3hIdG1sOwoKICAgIC8vIEFwcGVuZCB0aGUgY3VzdG9tIHNlYXJjaCBib3ggdG8gdGhlIGJvZHkKICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQoY3VzdG9tU2VhcmNoRGl2KTsKCiAgICAvLyBOZXcgc2VhcmNoIGZ1bmN0aW9uCiAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY3VzdG9tLXNlYXJjaC1idXR0b24tbmV3JykuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCBmdW5jdGlvbigpIHsKICAgICAgICB2YXIgc2VhcmNoVGV4dCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjdXN0b20tc2VhcmNoLWlucHV0LW5ldycpLnZhbHVlOwogICAgICAgIHdpbmRvdy5vcGVuKCJodHRwczovL3d3dy5hdmNoZXZ5LmNvbS9uZXctdmVoaWNsZXMvP3E9IiArIGVuY29kZVVSSUNvbXBvbmVudChzZWFyY2hUZXh0KSwgJ19ibGFuaycpOwogICAgfSk7CgogICAgLy8gVXNlZCBzZWFyY2ggZnVuY3Rpb24KICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjdXN0b20tc2VhcmNoLWJ1dHRvbi11c2VkJykuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCBmdW5jdGlvbigpIHsKICAgICAgICB2YXIgc2VhcmNoVGV4dCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjdXN0b20tc2VhcmNoLWlucHV0LXVzZWQnKS52YWx1ZTsKICAgICAgICB3aW5kb3cub3BlbigiaHR0cHM6Ly93d3cuYXZjaGV2eS5jb20vdXNlZC12ZWhpY2xlcy8/cT0iICsgZW5jb2RlVVJJQ29tcG9uZW50KHNlYXJjaFRleHQpLCAnX2JsYW5rJyk7CiAgICB9KTsKCiAgICAvLyBDbG9zZSBmdW5jdGlvbgogICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2N1c3RvbS1zZWFyY2gtY2xvc2UnKS5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsIGZ1bmN0aW9uKCkgewogICAgICAgIC8vIEhpZGUgdGhlIGlucHV0IGJveAogICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjdXN0b20tc2VhcmNoJykuc3R5bGUuZGlzcGxheSA9ICdub25lJzsKICAgIH0pOwp9KSgpOwo="}, {"name": "Remove Elements and Modify Text", "options": {"check_for_updates": false, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/Reports/ResponseTimes.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711219720104}, "storage": {"ts": 1711470419071, "data": {}}, "enabled": true, "position": 3, "uuid": "4f817108-ffcf-42a9-bd2b-56a41c1eeb9a", "source": "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"}, {"name": "Insert Rep Name and check radio button.", "options": {"check_for_updates": false, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://apps.vinmanager.com/CarDashboard/Pages/LeadManagement/LogCallV2/LogCallV2.aspx*", "https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/LogCallV2/LogCallV2.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1707761335771}, "storage": {"ts": 1711470419071, "data": {}}, "enabled": true, "position": 4, "uuid": "94f5bf02-2250-425d-95e2-0713c11658c9", "source": "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"}, {"name": "Lead Killer with Working Notes", "options": {"check_for_updates": false, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/Reports/ResponseTimes.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711217817869}, "storage": {"ts": 1711470419071, "data": {}}, "enabled": true, "position": 5, "uuid": "615e8dfa-b370-424d-86e1-4deb3e148ea1", "source": "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"}, {"name": "Dynamic Tab Title Changer", "options": {"check_for_updates": true, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://apps.vinmanager.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx*", "https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/DeskLog.aspx", "https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/Reports/ResponseTimes.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711598186553}, "storage": {"ts": 1711470419072, "data": {}}, "enabled": true, "position": 6, "uuid": "c355092a-1f6e-4a57-9657-60ef16baf030", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBEeW5hbWljIFRhYiBUaXRsZSBDaGFuZ2VyCi8vIEBuYW1lc3BhY2UgICAgaHR0cDovL3RhbXBlcm1vbmtleS5uZXQvCi8vIEB2ZXJzaW9uICAgICAgMS4wCi8vIEBhdXRob3IgICAgICAgQWxleAovLyBAZGVzY3JpcHRpb24gIER5bmFtaWNhbGx5IGNoYW5nZSB0aGUgdGFiIHRpdGxlIG9uIGNvbnRlbnQgY2hhbmdlcwovLyBAbWF0Y2ggICAgICAgIGh0dHBzOi8vYXBwcy52aW5tYW5hZ2VyLmNvbS9DYXJEYXNoYm9hcmQvUGFnZXMvQ1JNL0N1c3RvbWVyRGFzaGJvYXJkLmFzcHgqCi8vIEBtYXRjaCAgICAgICAgaHR0cHM6Ly92aW5zb2x1dGlvbnMuYXBwLmNveGF1dG9pbmMuY29tL0NhckRhc2hib2FyZC9QYWdlcy9MZWFkTWFuYWdlbWVudC9EZXNrTG9nLmFzcHgKLy8gQG1hdGNoICAgICAgICBodHRwczovL3ZpbnNvbHV0aW9ucy5hcHAuY294YXV0b2luYy5jb20vQ2FyRGFzaGJvYXJkL1BhZ2VzL0xlYWRNYW5hZ2VtZW50L1JlcG9ydHMvUmVzcG9uc2VUaW1lcy5hc3B4KgovLyBAZ3JhbnQgICAgICAgIG5vbmUKLy8gPT0vVXNlclNjcmlwdD09CgooZnVuY3Rpb24oKSB7CiAgICAndXNlIHN0cmljdCc7CgogICAgY29uc3QgY3VzdG9tVGl0bGUgPSAiQW50ZWxvcGUgVmFsbGV5IENoZXZyb2xldCI7CiAgICBkb2N1bWVudC50aXRsZSA9IGN1c3RvbVRpdGxlOyAvLyBTZXQgaW5pdGlhbCB0aXRsZQoKICAgIC8vIEZ1bmN0aW9uIHRvIHVwZGF0ZSB0aXRsZSBpZiBpdCBjaGFuZ2VzCiAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBNdXRhdGlvbk9ic2VydmVyKG11dGF0aW9ucyA9PiB7CiAgICAgICAgaWYgKGRvY3VtZW50LnRpdGxlICE9PSBjdXN0b21UaXRsZSkgewogICAgICAgICAgICBkb2N1bWVudC50aXRsZSA9IGN1c3RvbVRpdGxlOwogICAgICAgIH0KICAgIH0pOwoKICAgIC8vIFN0YXJ0IG9ic2VydmluZyB0aGUgZG9jdW1lbnQgaGVhZCBmb3IgY2hhbmdlcyB0byB0aGUgdGl0bGUKICAgIG9ic2VydmVyLm9ic2VydmUoZG9jdW1lbnQucXVlcnlTZWxlY3RvcigndGl0bGUnKSwgeyBjaGlsZExpc3Q6IHRydWUgfSk7Cn0pKCk7Cg=="}, {"name": "Tab to Popup Converter with Open RT, Advanced Remember Size, and <PERSON><PERSON><PERSON> Tooltip", "options": {"check_for_updates": true, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/Reports/ResponseTimes.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711326687383}, "storage": {"ts": 1711470419072, "data": {}}, "enabled": true, "position": 7, "file_url": "https://raw.githubusercontent.com/geomodi/tmpkscripts/main/Tab2PopuUp.user.js?token=GHSAT0AAAAAACKMSXMR7D66QQK2GVC2JAFYZMUNWPA", "uuid": "614df863-e838-4d57-bb7d-f8afb0bd79fa", "source": "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"}, {"name": "Message Box with Dealership Info from Airtable", "options": {"check_for_updates": true, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": ["airtable.com"], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": null}, "storage": {"ts": 1711470419073, "data": {}}, "enabled": true, "position": 8, "uuid": "4f632c4f-50f1-4fef-9c4b-eed56d05fb9e", "source": "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"}, {"name": "VinSo Faster Rooftops", "options": {"check_for_updates": false, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/DeskLog.aspx"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": ["githubusercontent.com"], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711726593329}, "storage": {"ts": 1711570246954, "data": {}}, "enabled": true, "position": 9, "uuid": "b87b38a8-76b6-45ec-acf1-4f4c138e0d9d", "source": "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"}, {"name": "TrueBDC Click to Call", "options": {"check_for_updates": true, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/*", "https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx*", "https://apps.vinmanager.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx*", "https://app.drivecentric.com*", "https://app1.drivecentric.com*", "https://app2.drivecentric.com*", "https://app3.drivecentric.com*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711225203371}, "storage": {"ts": 1711470419073, "data": {}}, "enabled": true, "position": 10, "uuid": "a3e69ed9-10a4-4e46-8b33-59ca6ec82e77", "source": "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"}, {"name": "TrueBDC Email Templates", "options": {"check_for_updates": true, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/sendemail.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711300476766}, "storage": {"ts": 1711470419074, "data": {}}, "enabled": true, "position": 11, "uuid": "cf5c13a9-0e72-4fdb-a927-7f832b16602d", "source": "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"}, {"name": "Auto Refresh with Configurable Timer, Pause, and Animation", "options": {"check_for_updates": true, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/LeadManagement/Reports/ResponseTimes.aspx*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": 1711828506419}, "storage": {"ts": 1711828473487, "data": {}}, "enabled": true, "position": 12, "uuid": "583b56ad-5325-45c6-bc4c-0fda57b6def0", "source": "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"}, {"name": "VinSo Light Appt Modifier", "options": {"check_for_updates": true, "user_modified": 1712346339785, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://apps.vinmanager.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1712346191878, "data": {}}, "enabled": true, "position": 13, "uuid": "08b3cd68-2c3c-4c13-9c3e-06f9c9e209a3", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBWaW5TbyBMaWdodCBBcHB0IE1vZGlmaWVyCi8vIEBuYW1lc3BhY2UgICAgaHR0cDovL3RhbXBlcm1vbmtleS5uZXQvCi8vIEB2ZXJzaW9uICAgICAgMC4xCi8vIEBkZXNjcmlwdGlvbiAgVmluU28gTGlnaHQgQXBwdCBNb29kaWZpZXIKLy8gQGF1dGhvciAgICAgICBBbGV4Ci8vIEBtYXRjaCAgICAgICAgaHR0cHM6Ly9hcHBzLnZpbm1hbmFnZXIuY29tL0NhckRhc2hib2FyZC9QYWdlcy9DUk0vQ3VzdG9tZXJEYXNoYm9hcmQuYXNweCoKLy8gQGdyYW50ICAgICAgICBub25lCi8vID09L1VzZXJTY3JpcHQ9PQoKKGZ1bmN0aW9uKCkgewogICAgJ3VzZSBzdHJpY3QnOwoKICAgIC8vIEZpbmQgYWxsIDxhPiB0YWdzIHdpdGggdGhlICJWaWV3IEFwcG9pbnRtZW50IiB0aXRsZQogICAgY29uc3QgYXBwb2ludG1lbnRMaW5rcyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJ2FbdGl0bGU9IlZpZXcgQXBwb2ludG1lbnQiXScpOwoKICAgIC8vIEFkZCBhIGNsaWNrIGV2ZW50IGxpc3RlbmVyIHRvIGVhY2ggbGluawogICAgYXBwb2ludG1lbnRMaW5rcy5mb3JFYWNoKGxpbmsgPT4gewogICAgICAgIGxpbmsuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVBcHBvaW50bWVudENsaWNrKTsKICAgIH0pOwoKICAgIGZ1bmN0aW9uIGhhbmRsZUFwcG9pbnRtZW50Q2xpY2soZXZlbnQpIHsKICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpOyAvLyBQcmV2ZW50IHRoZSBkZWZhdWx0IGxpbmsgYmVoYXZpb3IKCiAgICAgICAgLy8gRXh0cmFjdCB0aGUgcmVsZXZhbnQgcGFyYW1ldGVycyBmcm9tIHRoZSBjbGlja2VkIGxpbmsncyBocmVmCiAgICAgICAgY29uc3QgdXJsID0gbmV3IFVSTChldmVudC50YXJnZXQuaHJlZiwgd2luZG93LmxvY2F0aW9uLmhyZWYpOwogICAgICAgIGNvbnN0IGFwcG9pbnRtZW50SWQgPSB1cmwuc2VhcmNoUGFyYW1zLmdldCgnSUQnKTsKICAgICAgICBjb25zdCBhdXRvTGVhZElkID0gdXJsLnNlYXJjaFBhcmFtcy5nZXQoJ2FsaWQnKTsKCiAgICAgICAgLy8gQ29uc3RydWN0IHRoZSBuZXcgd2luZG93IFVSTCB3aXRoIHRoZSBleHRyYWN0ZWQgcGFyYW1ldGVycwogICAgICAgIGNvbnN0IG5ld1dpbmRvd1VybCA9IGBodHRwczovL3ZpbnNvbHV0aW9ucy5hcHAuY294YXV0b2luYy5jb20vQ2FyRGFzaGJvYXJkL1BhZ2VzL1JpbXMyLmFzcHg/VXJsU2V0dGluZ05hbWU9V29ya2Zsb3cuQ2FyRGFzaGJvYXJkLkFwcG9pbnRtZW50LlJpbXMuVXJsJkNvbXBvbmVudD1WaWV3JkFwcG9pbnRtZW50SWQ9JHthcHBvaW50bWVudElkfSZEZWFsZXJJZD0xNDgxMyZSZXF1ZXN0aW5nVXNlcklkPTEyMTc0MDQmQ2FuQ29tcGxldGU9VHJ1ZSZDYW5Db25maXJtPVRydWVgOwoKICAgICAgICAvLyBPcGVuIHRoZSBuZXcgd2luZG93CiAgICAgICAgd2luZG93Lm9wZW4obmV3V2luZG93VXJsKTsKICAgIH0KfSkoKTs="}, {"name": "VinSo New Leads PopUp", "options": {"check_for_updates": true, "comment": null, "compat_foreach": false, "compat_metadata": false, "compat_powerful_this": null, "compat_wrappedjsobject": false, "compatopts_for_requires": true, "noframes": null, "override": {"merge_connects": true, "merge_excludes": true, "merge_includes": true, "merge_matches": true, "orig_connects": [], "orig_excludes": [], "orig_includes": [], "orig_matches": ["*://*/*"], "orig_noframes": null, "orig_run_at": "document-idle", "use_blockers": [], "use_connects": [], "use_excludes": [], "use_includes": [], "use_matches": []}, "run_at": null, "sandbox": null, "tab_types": null, "unwrap": null, "user_modified": null}, "storage": {"ts": 1711977290524, "data": {}}, "enabled": true, "position": 14, "uuid": "f81554ca-e3a0-4795-87f7-2c44481ef0a5", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBWaW5TbyBOZXcgTGVhZHMgUG9wVXAKLy8gQHZlcnNpb24gICAgICAxLjAKLy8gQGRlc2NyaXB0aW9uICBWaW5TbyBOZXcgTGVhZHMgUG9wVXAKLy8gQGF1dGhvciAgICAgICBBbGV4Ci8vIEBtYXRjaCAgICAgICAgKjovLyovKgovLyBAZ3JhbnQgICAgICAgIG5vbmUKLy8gPT0vVXNlclNjcmlwdD09CgooZnVuY3Rpb24oKSB7CiAgICAndXNlIHN0cmljdCc7CgogICAgLy8gRnVuY3Rpb24gdG8gZm9ybWF0IHRoZSBkYXRlIGFzIE1NL0REL1lZWVkKICAgIGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZSkgewogICAgICAgIGNvbnN0IG1vbnRoID0gKGRhdGUuZ2V0TW9udGgoKSArIDEpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgICBjb25zdCBkYXkgPSBkYXRlLmdldERhdGUoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgICAgY29uc3QgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTsKICAgICAgICByZXR1cm4gYCR7bW9udGh9LyR7ZGF5fS8ke3llYXJ9YDsKICAgIH0KCiAgICAvLyBGdW5jdGlvbiB0byBjcmVhdGUgdGhlIHBvcHVwIHdpdGggdGhlIGN1c3RvbSBVUkwKICAgIGZ1bmN0aW9uIGNyZWF0ZVBvcHVwKCkgewogICAgICAgIGNvbnN0IGN1cnJlbnREYXRlID0gbmV3IERhdGUoKTsKICAgICAgICBjb25zdCBuZXh0RGF0ZSA9IG5ldyBEYXRlKGN1cnJlbnREYXRlKTsKICAgICAgICBuZXh0RGF0ZS5zZXREYXRlKGN1cnJlbnREYXRlLmdldERhdGUoKSArIDEpOwoKICAgICAgICBjb25zdCBmcm9tRGF0ZSA9IGZvcm1hdERhdGUoY3VycmVudERhdGUpOwogICAgICAgIGNvbnN0IHRvRGF0ZSA9IGZvcm1hdERhdGUobmV4dERhdGUpOwoKICAgICAgICBjb25zdCBiYXNlVXJsID0gJ2h0dHBzOi8vdmluc29sdXRpb25zLmFwcC5jb3hhdXRvaW5jLmNvbS9DYXJEYXNoYm9hcmQvUGFnZXMvTGVhZE1hbmFnZW1lbnQvUmVwb3J0cy9SZXNwb25zZVRpbWVzLmFzcHgnOwogICAgICAgIGNvbnN0IGN1c3RvbVVybCA9IGAke2Jhc2VVcmx9P2Zyb209JHtlbmNvZGVVUklDb21wb25lbnQoZnJvbURhdGUpfSZ0bz0ke2VuY29kZVVSSUNvbXBvbmVudCh0b0RhdGUpfSZ0YXJnZXQ9Z25gOwoKICAgICAgICB3aW5kb3cub3BlbihjdXN0b21VcmwsICdfYmxhbmsnLCAnd2lkdGg9ODAwLGhlaWdodD02MDAnKTsKICAgIH0KCiAgICAvLyBFdmVudCBsaXN0ZW5lciBmb3IgQ3RybCtBbHQrOCBrZXkgY29tYmluYXRpb24KICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBmdW5jdGlvbihldmVudCkgewogICAgICAgIGlmIChldmVudC5jdHJsS2V5ICYmIGV2ZW50LmFsdEtleSAmJiBldmVudC5rZXlDb2RlID09PSA1NikgewogICAgICAgICAgICBjcmVhdGVQb3B1cCgpOwogICAgICAgIH0KICAgIH0pOwp9KSgpOw=="}]}