<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrueBDC Test</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <img src="icons/icon48.png" alt="TrueBDC Logo">
                <h1>TrueBDC CRM Suite</h1>
            </div>
            <div class="version">v1.0.0</div>
        </header>

        <nav class="tabs">
            <button class="tab-btn active" data-tab="scripts">
                <img src="img/eLeads.png" alt="eLeads" class="tab-icon">
                eLeads
            </button>
            <button class="tab-btn" data-tab="vinso">
                <img src="img/VinSo.png" alt="VinSo" class="tab-icon">
                VinSo
            </button>
            <button class="tab-btn" data-tab="settings">Settings</button>
            <button class="tab-btn" data-tab="profiles">Profiles</button>
        </nav>

        <main class="content">
            <!-- eLeads Tab -->
            <div id="scripts-tab" class="tab-content active">
                <div class="script-group">
                    <h3>Communication</h3>
                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">TrueBDC Click to Call</span>
                            <span class="script-desc">Adds click-to-call functionality with dial tracking</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="click-to-call" data-script="clickToCall" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- VinSo Tab -->
            <div id="vinso-tab" class="tab-content">
                <div class="script-group">
                    <h3>Communication</h3>
                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">TrueBDC Click to Call</span>
                            <span class="script-desc">Adds click-to-call functionality with dial tracking for VinSolutions</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="vinso-click-to-call" data-script="clickToCall" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Simple tab switching for testing
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.closest('.tab-btn').dataset.tab;
                
                // Update tab buttons
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                e.target.closest('.tab-btn').classList.add('active');
                
                // Update tab content
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                document.getElementById(`${tabName}-tab`).classList.add('active');
            });
        });
    </script>
</body>
</html>
