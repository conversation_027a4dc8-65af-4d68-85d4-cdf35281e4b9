const fs = require('fs');

// Read the tampermonkey script file
const data = fs.readFileSync('read.txt', 'utf8');
const scriptCollection = JSON.parse(data);

console.log('=== VINSOLUTIONS SCRIPT COLLECTION ANALYSIS ===\n');
console.log('Total scripts found:', scriptCollection.scripts.length, '\n');

scriptCollection.scripts.forEach((script, index) => {
    console.log((index + 1) + '. Script Name:', script.name);
    console.log('   Enabled:', script.enabled);
    
    // Decode base64 source code
    const sourceCode = Buffer.from(script.source, 'base64').toString('utf8');
    
    // Extract key information from the source code
    const lines = sourceCode.split('\n');
    const userScriptHeader = lines.filter(line => line.includes('@'));
    
    console.log('   Script Details:');
    userScriptHeader.forEach(line => {
        if (line.includes('@name') || line.includes('@description') || line.includes('@match') || line.includes('@version')) {
            console.log('     ' + line.trim());
        }
    });
    
    // Extract main functionality description
    const descriptionLine = userScriptHeader.find(line => line.includes('@description'));
    if (descriptionLine) {
        const description = descriptionLine.replace(/.*@description\s+/, '').trim();
        console.log('   Purpose:', description);
    }
    
    // Extract target URLs
    const matchLines = userScriptHeader.filter(line => line.includes('@match'));
    if (matchLines.length > 0) {
        console.log('   Target URLs:');
        matchLines.forEach(match => {
            const url = match.replace(/.*@match\s+/, '').trim();
            console.log('     -', url);
        });
    }
    
    console.log('\n' + '='.repeat(80) + '\n');
});
