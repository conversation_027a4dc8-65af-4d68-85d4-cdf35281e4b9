<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VinSolutions Test - Customer Dashboard</title>
    <link rel="stylesheet" href="styles/content.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        #buyerColumn {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            background: #fafafa;
        }
        .standardText {
            font-size: 14px;
            line-height: 1.4;
        }
        .customerName {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VinSolutions Customer Dashboard Test</h1>
        <p>This page simulates the VinSolutions customer dashboard structure to test our enhanced Click to Call functionality.</p>
        
        <div id="buyerColumn">
            <span class="standardText">Buyer</span>&nbsp;&nbsp;
            <input type="image" name="ActiveLeadPanelWONotesAndHistory1$_EditBuyer" id="ActiveLeadPanelWONotesAndHistory1__EditBuyer" title="Edit Buyer" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" style="height:25px;width:25px;"><br>
            <span id="ActiveLeadPanelWONotesAndHistory1__BuyerName" class="customerName">Alex Perez<br></span>
            
            <span id="ActiveLeadPanelWONotesAndHistory1__BuyerInfoDetails" class="standardText">Eve: (*************<br>Cell: (*************<br><EMAIL><br>513 E County Road 138<br>Midland, TX 797067058<br></span><br>                                            
        </div>

        <div id="cobuyer">
            <span class="standardText">Co-Buyer</span>&nbsp;&nbsp;<br>
            <span class="customerName">Maria Perez<br></span>
            <span class="standardText">Home: (*************<br>Work: (*************<br><EMAIL><br></span>
        </div>

        <div class="test-section">
            <h3>Additional Test Numbers</h3>
            <p>Regular phone numbers: ************, (*************</p>
            <p>VinSolutions format: Mobile: (*************</p>
            <p>Office: (*************</p>
        </div>
    </div>

    <!-- Load our Click to Call script for testing -->
    <script src="scripts/utils.js"></script>
    <script>
        // Simulate VinSolutions URL for testing
        Object.defineProperty(window.location, 'href', {
            writable: true,
            value: 'https://vinsolutions.app.coxautoinc.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx'
        });
        
        // Initialize TrueBDCUtils for testing
        if (typeof TrueBDCUtils === 'undefined') {
            window.TrueBDCUtils = {
                log: (msg, data) => console.log('[TrueBDC]', msg, data),
                error: (msg, error) => console.error('[TrueBDC]', msg, error),
                logActivity: (action, data) => console.log('[Activity]', action, data),
                createElement: (tag, attrs, styles) => {
                    const el = document.createElement(tag);
                    if (attrs) Object.keys(attrs).forEach(attr => el.setAttribute(attr, attrs[attr]));
                    if (styles) Object.keys(styles).forEach(style => el.style[style] = styles[style]);
                    return el;
                },
                formatPhoneNumber: (number) => {
                    const cleaned = number.replace(/\D/g, '');
                    return `(${cleaned.slice(0,3)}) ${cleaned.slice(3,6)}-${cleaned.slice(6)}`;
                },
                getFrameContext: () => ({ isIframe: false, isMainFrame: true })
            };
        }
    </script>
    <script src="scripts/click-to-call.js"></script>
    <script>
        // Initialize Click to Call for testing
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Initializing Click to Call test...');
            const clickToCall = new ClickToCall();
            
            // Add a button to manually trigger scanning
            const testButton = document.createElement('button');
            testButton.textContent = 'Rescan for Phone Numbers';
            testButton.style.margin = '10px';
            testButton.style.padding = '10px 20px';
            testButton.style.background = '#007bff';
            testButton.style.color = 'white';
            testButton.style.border = 'none';
            testButton.style.borderRadius = '5px';
            testButton.style.cursor = 'pointer';
            testButton.onclick = () => {
                console.log('Manual rescan triggered...');
                clickToCall.scanForPhoneNumbers();
            };
            document.body.appendChild(testButton);
        });
    </script>
</body>
</html>
