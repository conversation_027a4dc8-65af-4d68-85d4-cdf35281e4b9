/* TrueBDC CRM Automation Suite - Content Script Styles */

/* Modal Styles */
.truebdc-modal {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 999999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.truebdc-modal-content {
    background-color: white !important;
    border-radius: 8px !important;
    padding: 20px !important;
    max-width: 500px !important;
    max-height: 80vh !important;
    overflow: auto !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    margin: 20px !important;
}

.truebdc-modal-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 15px !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid #eee !important;
}

.truebdc-modal-close {
    background: none !important;
    border: none !important;
    font-size: 24px !important;
    cursor: pointer !important;
    color: #999 !important;
    padding: 0 !important;
    width: 30px !important;
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.truebdc-modal-close:hover {
    color: #333 !important;
}

/* Tooltip Styles */
.truebdc-tooltip {
    position: absolute !important;
    background-color: #333 !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    z-index: 999999 !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    transition: opacity 0.3s !important;
    pointer-events: none !important;
}

.truebdc-tooltip::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 50% !important;
    margin-left: -5px !important;
    border-width: 5px !important;
    border-style: solid !important;
    border-color: #333 transparent transparent transparent !important;
}

/* Click to Call Styles */
.truebdc-phone-icon {
    display: inline-block !important;
    margin-left: 5px !important;
    cursor: pointer !important;
    color: #007bff !important;
    text-decoration: none !important;
    font-size: 14px !important;
    vertical-align: middle !important;
}

.truebdc-phone-icon:hover {
    color: #0056b3 !important;
}

.truebdc-phone-icon img {
    width: 16px !important;
    height: 16px !important;
    vertical-align: middle !important;
}

/* Auto Refresh Timer Styles */
.truebdc-refresh-timer {
    position: fixed !important;
    top: 10px !important;
    right: 10px !important;
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    z-index: 999998 !important;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3) !important;
    cursor: pointer !important;
    user-select: none !important;
}

.truebdc-refresh-timer:hover {
    background: linear-gradient(135deg, #0056b3, #004085) !important;
}

.truebdc-refresh-timer.paused {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
}

.truebdc-refresh-timer.paused:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00) !important;
}

/* Progress Bar */
.truebdc-progress-bar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 3px !important;
    background: linear-gradient(90deg, #007bff, #28a745) !important;
    z-index: 999999 !important;
    transition: width 1s linear !important;
}

/* Faster Rooftops Styles */
.truebdc-rooftop-dialog {
    background: white !important;
    border: 2px solid #007bff !important;
    border-radius: 12px !important;
    padding: 20px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
    max-width: 600px !important;
    width: 90% !important;
}

.truebdc-rooftop-title {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: white !important;
    padding: 10px 15px !important;
    margin: -20px -20px 20px -20px !important;
    border-radius: 10px 10px 0 0 !important;
    text-align: center !important;
    font-weight: 600 !important;
    font-size: 16px !important;
}

.truebdc-button {
    background: #007bff !important;
    color: white !important;
    border: none !important;
    padding: 10px 20px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    margin: 5px !important;
    transition: background-color 0.2s !important;
}

.truebdc-button:hover {
    background: #0056b3 !important;
}

.truebdc-button.secondary {
    background: #6c757d !important;
}

.truebdc-button.secondary:hover {
    background: #545b62 !important;
}

.truebdc-button.success {
    background: #28a745 !important;
}

.truebdc-button.success:hover {
    background: #1e7e34 !important;
}

.truebdc-button.warning {
    background: #ffc107 !important;
    color: #212529 !important;
}

.truebdc-button.warning:hover {
    background: #e0a800 !important;
}

.truebdc-button.danger {
    background: #dc3545 !important;
}

.truebdc-button.danger:hover {
    background: #c82333 !important;
}

/* Form Styles */
.truebdc-form-group {
    margin-bottom: 15px !important;
}

.truebdc-label {
    display: block !important;
    margin-bottom: 5px !important;
    font-weight: 500 !important;
    color: #333 !important;
    font-size: 14px !important;
}

.truebdc-input,
.truebdc-select {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    transition: border-color 0.2s !important;
}

.truebdc-input:focus,
.truebdc-select:focus {
    outline: none !important;
    border-color: #007bff !important;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25) !important;
}

/* List Styles */
.truebdc-list {
    max-height: 300px !important;
    overflow-y: auto !important;
    border: 1px solid #e9ecef !important;
    border-radius: 4px !important;
    padding: 10px !important;
    background: #f8f9fa !important;
}

.truebdc-list ul {
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.truebdc-list li {
    padding: 5px 0 !important;
    border-bottom: 1px solid #e9ecef !important;
    font-size: 13px !important;
}

.truebdc-list li:last-child {
    border-bottom: none !important;
}

.truebdc-list li.duplicate {
    color: #dc3545 !important;
    font-weight: 500 !important;
}

/* Status Messages */
.truebdc-status {
    padding: 10px 15px !important;
    border-radius: 4px !important;
    margin: 10px 0 !important;
    font-size: 14px !important;
}

.truebdc-status.success {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

.truebdc-status.error {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

.truebdc-status.warning {
    background: #fff3cd !important;
    color: #856404 !important;
    border: 1px solid #ffeaa7 !important;
}

.truebdc-status.info {
    background: #d1ecf1 !important;
    color: #0c5460 !important;
    border: 1px solid #bee5eb !important;
}

/* Loading Spinner */
.truebdc-spinner {
    display: inline-block !important;
    width: 20px !important;
    height: 20px !important;
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #007bff !important;
    border-radius: 50% !important;
    animation: truebdc-spin 1s linear infinite !important;
}

@keyframes truebdc-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Checkbox Styles */
.truebdc-checkbox {
    display: flex !important;
    align-items: center !important;
    margin: 10px 0 !important;
}

.truebdc-checkbox input[type="checkbox"] {
    margin-right: 8px !important;
    width: 16px !important;
    height: 16px !important;
}

.truebdc-checkbox label {
    font-size: 14px !important;
    color: #333 !important;
    cursor: pointer !important;
    user-select: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .truebdc-modal-content {
        max-width: 95% !important;
        margin: 10px !important;
        padding: 15px !important;
    }
    
    .truebdc-rooftop-dialog {
        width: 95% !important;
        padding: 15px !important;
    }
    
    .truebdc-refresh-timer {
        top: 5px !important;
        right: 5px !important;
        padding: 6px 10px !important;
        font-size: 11px !important;
    }
}

/* Hide default scrollbars in modal content */
.truebdc-modal-content::-webkit-scrollbar {
    width: 6px !important;
}

.truebdc-modal-content::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
}

.truebdc-modal-content::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 3px !important;
}

.truebdc-modal-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}
