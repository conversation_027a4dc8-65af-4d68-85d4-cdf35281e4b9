// TrueBDC CRM Automation Suite - Click to Call

class ClickToCall {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.lastClickedNumber = null;
        this.lastClickTime = 0;
        this.clickThreshold = 1000 * 60 * 5; // 5 minutes threshold for consecutive clicks
        this.phoneIcons = new Map();
        this.observer = null;
        
        this.init();
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            TrueBDCUtils.log('Initializing TrueBDC Click to Call', {
                frameContext: frameContext
            });

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupPhoneDetection();
                this.isActive = true;

                TrueBDCUtils.log('TrueBDC Click to Call activated', {
                    frameContext: frameContext
                });
                TrueBDCUtils.logActivity('click_to_call_activated', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('<PERSON><PERSON><PERSON> Click to Call not activated - unsupported page', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize TrueBDC Click to Call', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            // eLeadCRM
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track/i,
            // VinSolutions - Specific URLs from Tampermonkey script
            /vinsolutions\.app\.coxautoinc\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i,
            /apps\.vinmanager\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i,
            // DriveCentric (from Tampermonkey script)
            /app\.drivecentric\.com/i,
            /app[123]\.drivecentric\.com/i
        ];

        return supportedPatterns.some(pattern => pattern.test(url));
    }

    setupPhoneDetection() {
        // Initial scan for phone numbers
        this.scanForPhoneNumbers();

        // Set up mutation observer to detect new phone numbers
        this.observer = new MutationObserver((mutations) => {
            let shouldScan = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any added nodes contain text
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === Node.TEXT_NODE || 
                            (node.nodeType === Node.ELEMENT_NODE && node.textContent)) {
                            shouldScan = true;
                            break;
                        }
                    }
                }
            });

            if (shouldScan) {
                // Debounce the scanning to avoid excessive calls
                clearTimeout(this.scanTimeout);
                this.scanTimeout = setTimeout(() => {
                    this.scanForPhoneNumbers();
                }, 500);
            }
        });

        this.observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Periodic scan as fallback
        this.scanInterval = setInterval(() => {
            this.scanForPhoneNumbers();
        }, 10000); // Every 10 seconds
    }

    scanForPhoneNumbers() {
        try {
            // First, handle VinSolutions-specific elements
            this.scanVinSolutionsElements();

            // Then, handle general phone number patterns
            const phonePatterns = [
                /\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b/g, // ************, ************, ************
                /\(\d{3}\)\s?\d{3}[-.\s]?\d{4}/g,     // (*************
                /\b\d{10}\b/g,                        // 1234567890
                /\+1[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}/g // ******-456-7890
            ];

            const textNodes = this.getTextNodes(document.body);

            textNodes.forEach(node => {
                const text = node.textContent;
                let hasPhone = false;

                phonePatterns.forEach(pattern => {
                    const matches = text.match(pattern);
                    if (matches) {
                        matches.forEach(match => {
                            const cleanNumber = this.cleanPhoneNumber(match);
                            if (this.isValidPhoneNumber(cleanNumber)) {
                                this.addClickToCallIcon(node, match, cleanNumber);
                                hasPhone = true;
                            }
                        });
                    }
                });
            });

        } catch (error) {
            TrueBDCUtils.error('Error scanning for phone numbers', error);
        }
    }

    scanVinSolutionsElements() {
        // Handle VinSolutions-specific phone number elements
        const url = window.location.href;
        if (/vinsolutions\.app\.coxautoinc\.com|apps\.vinmanager\.com/i.test(url)) {
            // Target VinSolutions specific phone number elements
            document.querySelectorAll('span[analyticsdetect="DealCard|Navigate|Phone"].phone-detail').forEach(span => {
                const originalPhoneNumber = span.textContent.trim();
                if (originalPhoneNumber && !span.dataset.truebdcConverted) {
                    // Mark the span as processed to avoid re-processing
                    span.dataset.truebdcConverted = true;
                    const cleanNumber = this.cleanPhoneNumber(originalPhoneNumber);
                    if (this.isValidPhoneNumber(cleanNumber)) {
                        this.addClickToCallIcon(span, originalPhoneNumber, cleanNumber);
                    }
                }
            });
        }
    }

    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    // Skip script and style elements
                    const parent = node.parentElement;
                    if (parent && (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    // Skip if already processed
                    if (parent && parent.querySelector('.truebdc-phone-icon')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        return textNodes;
    }

    addClickToCallIcon(textNode, originalFormat, cleanNumber) {
        try {
            const parent = textNode.parentElement;
            if (!parent) return;

            // Check if icon already exists for this number
            const iconId = `phone-icon-${cleanNumber}`;
            if (document.getElementById(iconId)) return;

            // Create click-to-call icon
            const icon = TrueBDCUtils.createElement('a', {
                id: iconId,
                href: `tel:+1${cleanNumber}`,
                class: 'truebdc-phone-icon',
                title: 'Click to call',
                'data-phone': cleanNumber,
                'data-original': originalFormat
            });

            // Phone icon using Unicode symbol (more compatible)
            icon.innerHTML = '📞';

            // Add click handler
            icon.addEventListener('click', (e) => {
                e.preventDefault();
                this.handlePhoneClick(cleanNumber, originalFormat, e);
            });

            // Insert icon after the text
            parent.insertBefore(icon, textNode.nextSibling);
            
            this.phoneIcons.set(cleanNumber, icon);

        } catch (error) {
            TrueBDCUtils.error('Error adding click-to-call icon', error);
        }
    }

    async handlePhoneClick(phoneNumber, originalFormat, event) {
        try {
            const now = Date.now();
            
            // Update click count and check for duplicates
            const shouldProceed = await this.updateClickCount(phoneNumber, event);
            if (!shouldProceed) return;

            // Show tooltip
            this.showTooltip(event.target, `Dialing ${TrueBDCUtils.formatPhoneNumber(phoneNumber)}...`);

            // Initiate call
            window.open(`tel:+1${phoneNumber}`, '_self');

            // Log activity
            TrueBDCUtils.logActivity('phone_call_initiated', {
                phoneNumber: phoneNumber,
                originalFormat: originalFormat,
                timestamp: new Date().toISOString()
            });

            TrueBDCUtils.log('Phone call initiated', { 
                phoneNumber: phoneNumber,
                originalFormat: originalFormat 
            });

        } catch (error) {
            TrueBDCUtils.error('Error handling phone click', error);
            this.showTooltip(event.target, 'Error initiating call', 'error');
        }
    }

    async updateClickCount(phoneNumber, event) {
        const now = Date.now();
        const countKey = `dialCount_${phoneNumber}`;
        
        try {
            // Get current count from storage
            const result = await chrome.storage.local.get(countKey);
            let currentCount = parseInt(result[countKey], 10) || 0;

            // Check if this is a consecutive click
            if (phoneNumber !== this.lastClickedNumber || 
                (now - this.lastClickTime) > this.clickThreshold) {
                currentCount = 1;
            } else {
                if (currentCount === 2) {
                    // Show confirmation for 3rd consecutive call
                    const confirmDial = confirm(
                        `You're about to dial this number for the 3rd time in a row. Are you sure you want to dial once more?`
                    );
                    if (!confirmDial) {
                        this.showTooltip(event.target, `Dialing cancelled for ${TrueBDCUtils.formatPhoneNumber(phoneNumber)}.`);
                        return false;
                    }
                }
                currentCount++;
            }

            // Save updated count
            await chrome.storage.local.set({ [countKey]: currentCount });
            
            // Update tracking variables
            this.lastClickedNumber = phoneNumber;
            this.lastClickTime = now;

            // Show count tooltip
            this.showTooltip(
                event.target, 
                `You have dialed ${TrueBDCUtils.formatPhoneNumber(phoneNumber)} ${currentCount} time(s).`
            );

            return true;

        } catch (error) {
            TrueBDCUtils.error('Error updating click count', error);
            return true; // Proceed with call even if count tracking fails
        }
    }

    showTooltip(element, text, type = 'info') {
        const tooltip = TrueBDCUtils.createElement('div', {
            class: 'truebdc-tooltip'
        }, {
            position: 'absolute',
            backgroundColor: type === 'error' ? '#dc3545' : '#333',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: '999999',
            whiteSpace: 'nowrap',
            opacity: '0',
            transition: 'opacity 0.3s',
            pointerEvents: 'none'
        });
        
        tooltip.textContent = text;
        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
        tooltip.style.left = `${rect.left + window.scrollX}px`;
        tooltip.style.opacity = '1';

        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 3000);
    }

    cleanPhoneNumber(phone) {
        if (!phone) return '';
        return phone.replace(/\D/g, '');
    }

    isValidPhoneNumber(cleanNumber) {
        // Must be 10 digits (US phone number)
        return /^\d{10}$/.test(cleanNumber);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('Click to Call settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                // Re-scan for phone numbers on new page
                setTimeout(() => {
                    this.scanForPhoneNumbers();
                }, 1000);
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Stop observing
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            // Clear intervals and timeouts
            if (this.scanInterval) {
                clearInterval(this.scanInterval);
                this.scanInterval = null;
            }

            if (this.scanTimeout) {
                clearTimeout(this.scanTimeout);
                this.scanTimeout = null;
            }

            // Remove all phone icons
            this.phoneIcons.forEach((icon) => {
                if (icon.parentNode) {
                    icon.parentNode.removeChild(icon);
                }
            });
            this.phoneIcons.clear();

            this.isActive = false;
            
            TrueBDCUtils.log('TrueBDC Click to Call destroyed');
            TrueBDCUtils.logActivity('click_to_call_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying TrueBDC Click to Call', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            phoneIconsCount: this.phoneIcons.size,
            lastClickedNumber: this.lastClickedNumber,
            lastClickTime: this.lastClickTime
        };
    }
}

// Make class globally available
window.ClickToCall = ClickToCall;
