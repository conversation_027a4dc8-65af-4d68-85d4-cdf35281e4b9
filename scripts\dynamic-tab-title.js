// TrueBDC CRM Automation Suite - Dynamic Tab Title Changer

class DynamicTabTitle {
    constructor(settings = {}) {
        this.settings = settings;
        this.customTitle = null;
        this.observer = null;
        this.originalTitle = document.title;
        this.isActive = false;

        this.init();
    }

    async init() {
        try {
            TrueBDCUtils.log('Initializing Dynamic Tab Title Changer', {
                originalTitle: this.originalTitle
            });

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                // Get or prompt for custom title
                await this.ensureCustomTitle();

                if (this.customTitle) {
                    this.setCustomTitle();
                    this.setupTitleObserver();
                    this.isActive = true;

                    TrueBDCUtils.log('Dynamic Tab Title Changer activated');
                    TrueBDCUtils.logActivity('dynamic_tab_title_activated', {
                        customTitle: this.customTitle,
                        originalTitle: this.originalTitle
                    });
                } else {
                    TrueBDCUtils.log('Dynamic Tab Title Changer not activated - no custom title set');
                }
            } else {
                TrueBDCUtils.log('Dynamic Tab Title Changer not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Dynamic Tab Title Changer', error);
        }
    }

    async ensureCustomTitle() {
        try {
            // First check if we have a saved custom title in settings
            if (this.settings.dealershipName && this.settings.dealershipName.trim()) {
                this.customTitle = this.settings.dealershipName.trim();
                return;
            }

            // Check if we have a saved custom title in storage
            const result = await chrome.storage.local.get('customDealershipName');
            if (result.customDealershipName && result.customDealershipName.trim()) {
                this.customTitle = result.customDealershipName.trim();
                return;
            }

            // Prompt user for custom title
            const userTitle = prompt(
                'Dynamic Tab Title Changer\n\n' +
                'Please enter your dealership name for tab titles:\n' +
                '(This will be saved and used for all future tab title changes)',
                ''
            );

            if (userTitle && userTitle.trim()) {
                this.customTitle = userTitle.trim();

                // Save to storage
                await chrome.storage.local.set({ customDealershipName: this.customTitle });

                // Also update settings if possible
                if (this.settings) {
                    this.settings.dealershipName = this.customTitle;
                    await TrueBDCUtils.sendMessage('updateSettings', { settings: this.settings });
                }

                TrueBDCUtils.log('Custom dealership name saved', { customTitle: this.customTitle });
                TrueBDCUtils.logActivity('custom_dealership_name_set', {
                    customTitle: this.customTitle
                });
            } else {
                TrueBDCUtils.log('User cancelled custom title setup');
            }
        } catch (error) {
            TrueBDCUtils.error('Error ensuring custom title', error);
            // Fallback to generic default
            this.customTitle = 'CRM Dashboard';
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/weblink\/weblinkToday\.aspx/i,
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/reports\/Desklog\/Desklog\.aspx/i,
            /eleadcrm\.com\/rt\/MessengerClient\/Home\/Index/i,
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/elead_mail\/Mailbox\.aspx\?Type=M/i,
            /vinsolutions\.com/i // Support VinSolutions as well
        ];

        return supportedPatterns.some(pattern => pattern.test(url));
    }

    setCustomTitle() {
        if (document.title !== this.customTitle) {
            document.title = this.customTitle;
            TrueBDCUtils.log('Tab title changed', { 
                from: this.originalTitle, 
                to: this.customTitle 
            });
        }
    }

    setupTitleObserver() {
        // Observe changes to the title element
        const titleElement = document.querySelector('title');
        if (titleElement) {
            this.observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && document.title !== this.customTitle) {
                        TrueBDCUtils.log('Title change detected, restoring custom title', {
                            detectedTitle: document.title,
                            customTitle: this.customTitle
                        });
                        this.setCustomTitle();
                    }
                });
            });

            this.observer.observe(titleElement, { 
                childList: true, 
                characterData: true,
                subtree: true 
            });
        }

        // Also observe the document head for title changes
        const headElement = document.head;
        if (headElement) {
            const headObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeName === 'TITLE') {
                                // New title element added, observe it
                                this.observer.observe(node, { 
                                    childList: true, 
                                    characterData: true,
                                    subtree: true 
                                });
                                this.setCustomTitle();
                            }
                        });
                    }
                });
            });

            headObserver.observe(headElement, { childList: true });
        }

        // Periodic check as fallback
        this.titleCheckInterval = setInterval(() => {
            if (document.title !== this.customTitle) {
                this.setCustomTitle();
            }
        }, 2000);
    }

    async updateSettings(newSettings) {
        const oldTitle = this.customTitle;
        this.settings = { ...this.settings, ...newSettings };

        if (newSettings.dealershipName && newSettings.dealershipName.trim()) {
            this.customTitle = newSettings.dealershipName.trim();

            // Save to storage as well
            try {
                await chrome.storage.local.set({ customDealershipName: this.customTitle });
            } catch (error) {
                TrueBDCUtils.error('Error saving custom title to storage', error);
            }
        }

        if (oldTitle !== this.customTitle && this.isActive) {
            TrueBDCUtils.log('Updating custom title', {
                from: oldTitle,
                to: this.customTitle
            });
            this.setCustomTitle();

            TrueBDCUtils.logActivity('dynamic_tab_title_updated', {
                oldTitle: oldTitle,
                newTitle: this.customTitle
            });
        }
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                this.setCustomTitle();
            }
        } else {
            if (this.isActive) {
                this.restoreOriginalTitle();
                this.isActive = false;
            }
        }
    }

    restoreOriginalTitle() {
        if (this.originalTitle && document.title === this.customTitle) {
            document.title = this.originalTitle;
            TrueBDCUtils.log('Original title restored', { 
                originalTitle: this.originalTitle 
            });
        }
    }

    destroy() {
        try {
            // Stop observing
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            // Clear interval
            if (this.titleCheckInterval) {
                clearInterval(this.titleCheckInterval);
                this.titleCheckInterval = null;
            }

            // Restore original title
            this.restoreOriginalTitle();

            this.isActive = false;
            
            TrueBDCUtils.log('Dynamic Tab Title Changer destroyed');
            TrueBDCUtils.logActivity('dynamic_tab_title_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Dynamic Tab Title Changer', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            customTitle: this.customTitle,
            originalTitle: this.originalTitle,
            currentTitle: document.title,
            isSupported: this.isSupportedPage()
        };
    }
}

// Make class globally available
window.DynamicTabTitle = DynamicTabTitle;
