// TrueBDC CRM Automation Suite - Popup Script

class TrueBDCPopup {
    constructor() {
        this.currentTab = 'scripts'; // Default to eLeads tab
        this.settings = {};
        this.profiles = [];
        this.init();
    }

    async init() {
        await this.loadSettings();
        await this.loadProfiles();
        this.setupEventListeners();
        this.updateUI();
        this.loadScriptStates();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Script toggles
        document.querySelectorAll('input[data-script]').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                this.toggleScript(e.target.dataset.script, e.target.checked);
            });
        });

        // Settings
        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('reset-settings').addEventListener('click', () => {
            this.resetSettings();
        });

        // Profiles
        document.getElementById('create-profile').addEventListener('click', () => {
            this.createProfile();
        });

        document.getElementById('export-config').addEventListener('click', () => {
            this.exportConfig();
        });

        document.getElementById('import-config').addEventListener('click', () => {
            this.importConfig();
        });

        // Help and feedback
        document.getElementById('help-link').addEventListener('click', (e) => {
            e.preventDefault();
            this.showHelp();
        });

        document.getElementById('feedback-link').addEventListener('click', (e) => {
            e.preventDefault();
            this.showFeedback();
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;
    }

    async toggleScript(scriptName, enabled) {
        try {
            // Save script state
            await chrome.storage.local.set({
                [`script_${scriptName}`]: enabled
            });

            // Send message to content script
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'toggleScript',
                    script: scriptName,
                    enabled: enabled
                });
            }

            this.updateStatus(`${scriptName} ${enabled ? 'enabled' : 'disabled'}`);
        } catch (error) {
            console.error('Error toggling script:', error);
            this.updateStatus('Error updating script', 'error');
        }
    }

    async loadScriptStates() {
        try {
            const scripts = [
                // 'dynamicTabTitle', // Removed - handled by popup title changer
                'bypassRefresh',
                'clickToCall',
                'vinsoClickToCall',
                'tabToPopup',
                'autoRefresh',
                'callingText',
                'autoNavigation',
                'autoCloseReleaseNotes'
            ];

            for (const script of scripts) {
                const result = await chrome.storage.local.get(`script_${script}`);
                const enabled = result[`script_${script}`] || false;

                // Handle eLeads tab toggles
                const toggle = document.getElementById(script.replace(/([A-Z])/g, '-$1').toLowerCase());
                if (toggle) {
                    toggle.checked = enabled;
                }

                // Handle VinSo tab toggles (same script state, different UI elements)
                const vinsoToggle = document.getElementById(`vinso-${script.replace(/([A-Z])/g, '-$1').toLowerCase()}`);
                if (vinsoToggle) {
                    vinsoToggle.checked = enabled;
                }
            }
        } catch (error) {
            console.error('Error loading script states:', error);
        }
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.local.get('settings');
            this.settings = result.settings || {
                dealershipName: '',
                agentName: 'Rep',
                // Airtable settings - temporarily disabled but kept in storage
                airtableApiKey: '',
                airtableBaseId: 'appexR9tFKGHjSWNE',
                refreshInterval: 5,
                // Popup window settings
                popupWidth: 1200,
                popupHeight: 800,
                // popupAlwaysOnTop: false, // REMOVED - Chrome extension limitations
                // Auto Navigation - managed by script toggle only
            };

            // Update UI with loaded settings
            document.getElementById('dealership-name').value = this.settings.dealershipName || '';
            document.getElementById('agent-name').value = this.settings.agentName || 'Rep';
            // Airtable fields - temporarily disabled
            // document.getElementById('airtable-api-key').value = this.settings.airtableApiKey;
            // document.getElementById('airtable-base-id').value = this.settings.airtableBaseId;
            document.getElementById('refresh-interval').value = this.settings.refreshInterval || 5;

            // Popup window settings
            document.getElementById('popup-width').value = this.settings.popupWidth || 1200;
            document.getElementById('popup-height').value = this.settings.popupHeight || 800;
            // Always on top removed - Chrome extension limitations

            // Auto Navigation - managed by script toggle only

            // Load and display current saved popup configurations
            await this.loadCurrentPopupDimensions();
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    async saveSettings() {
        try {
            this.settings = {
                dealershipName: document.getElementById('dealership-name').value,
                agentName: document.getElementById('agent-name').value,
                // Keep existing Airtable settings in storage (temporarily disabled in UI)
                airtableApiKey: this.settings.airtableApiKey || '',
                airtableBaseId: this.settings.airtableBaseId || 'appexR9tFKGHjSWNE',
                refreshInterval: parseInt(document.getElementById('refresh-interval').value),
                // Popup window settings
                popupWidth: parseInt(document.getElementById('popup-width').value),
                popupHeight: parseInt(document.getElementById('popup-height').value),
                // popupAlwaysOnTop removed - Chrome extension limitations
                // Auto Navigation - managed by script toggle only
            };

            await chrome.storage.local.set({ settings: this.settings });

            // Update default popup configuration in Tab to Popup converter
            await this.updateDefaultPopupConfig();

            // Send updated settings to content scripts
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'updateSettings',
                    settings: this.settings
                });
            }

            this.updateStatus('Settings saved successfully');
        } catch (error) {
            console.error('Error saving settings:', error);
            this.updateStatus('Error saving settings', 'error');
        }
    }

    async resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            try {
                await chrome.storage.local.remove('settings');
                await this.loadSettings();
                this.updateStatus('Settings reset to defaults');
            } catch (error) {
                console.error('Error resetting settings:', error);
                this.updateStatus('Error resetting settings', 'error');
            }
        }
    }

    async loadProfiles() {
        try {
            const result = await chrome.storage.local.get('profiles');
            this.profiles = result.profiles || [];
            this.updateProfilesList();
        } catch (error) {
            console.error('Error loading profiles:', error);
        }
    }

    updateProfilesList() {
        const profileList = document.getElementById('profile-list');
        profileList.innerHTML = '';

        if (this.profiles.length === 0) {
            profileList.innerHTML = '<p style="text-align: center; color: #6c757d; font-size: 13px;">No profiles created yet</p>';
            return;
        }

        this.profiles.forEach((profile, index) => {
            const profileItem = document.createElement('div');
            profileItem.className = 'profile-item';
            profileItem.innerHTML = `
                <div class="profile-info">
                    <div class="profile-name">${profile.name}</div>
                    <div class="profile-desc">${profile.dealership || 'No dealership set'}</div>
                </div>
                <div class="profile-actions">
                    <button class="btn btn-primary" onclick="trueBDCPopup.loadProfile(${index})">Load</button>
                    <button class="btn btn-secondary" onclick="trueBDCPopup.deleteProfile(${index})">Delete</button>
                </div>
            `;
            profileList.appendChild(profileItem);
        });
    }

    async createProfile() {
        const name = prompt('Enter profile name:');
        if (!name) return;

        const profile = {
            name: name,
            dealership: this.settings.dealershipName,
            settings: { ...this.settings },
            scripts: {}
        };

        // Save current script states
        const scripts = ['dynamicTabTitle', 'bypassRefresh', 'clickToCall', 'tabToPopup', 'autoRefresh', 'callingText'];
        for (const script of scripts) {
            const result = await chrome.storage.local.get(`script_${script}`);
            profile.scripts[script] = result[`script_${script}`] || false;
        }

        this.profiles.push(profile);
        await chrome.storage.local.set({ profiles: this.profiles });
        this.updateProfilesList();
        this.updateStatus('Profile created successfully');
    }

    async loadProfile(index) {
        if (index < 0 || index >= this.profiles.length) return;

        const profile = this.profiles[index];
        
        // Load settings
        this.settings = { ...profile.settings };
        await chrome.storage.local.set({ settings: this.settings });
        await this.loadSettings();

        // Load script states
        for (const [script, enabled] of Object.entries(profile.scripts)) {
            await chrome.storage.local.set({ [`script_${script}`]: enabled });
        }
        await this.loadScriptStates();

        this.updateStatus(`Profile "${profile.name}" loaded`);
    }

    async deleteProfile(index) {
        if (index < 0 || index >= this.profiles.length) return;
        
        if (confirm(`Delete profile "${this.profiles[index].name}"?`)) {
            this.profiles.splice(index, 1);
            await chrome.storage.local.set({ profiles: this.profiles });
            this.updateProfilesList();
            this.updateStatus('Profile deleted');
        }
    }

    exportConfig() {
        const config = {
            settings: this.settings,
            profiles: this.profiles,
            version: '1.0.0',
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `truebdc-config-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.updateStatus('Configuration exported');
    }

    importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const config = JSON.parse(text);

                if (config.settings) {
                    this.settings = config.settings;
                    await chrome.storage.local.set({ settings: this.settings });
                    await this.loadSettings();
                }

                if (config.profiles) {
                    this.profiles = config.profiles;
                    await chrome.storage.local.set({ profiles: this.profiles });
                    this.updateProfilesList();
                }

                this.updateStatus('Configuration imported successfully');
            } catch (error) {
                console.error('Error importing config:', error);
                this.updateStatus('Error importing configuration', 'error');
            }
        };
        input.click();
    }

    showHelp() {
        alert('TrueBDC CRM Automation Suite\n\nThis extension automates various tasks in automotive CRM systems.\n\nFor support, contact your system administrator.');
    }

    showFeedback() {
        alert('Feedback\n\nTo provide feedback or report issues, please contact your system administrator.');
    }

    updateStatus(message, type = 'success') {
        const statusElement = document.getElementById('status-message');
        statusElement.textContent = message;
        statusElement.style.color = type === 'error' ? '#dc3545' : '#28a745';
        
        setTimeout(() => {
            statusElement.textContent = 'Ready';
            statusElement.style.color = '#28a745';
        }, 3000);
    }

    async loadCurrentPopupDimensions() {
        try {
            // Load saved popup configurations from Tab to Popup converter
            const result = await chrome.storage.local.get('popupConfigs');
            const popupConfigs = result.popupConfigs || {};

            const currentDimensionsDiv = document.getElementById('current-dimensions');

            if (Object.keys(popupConfigs).length === 0) {
                currentDimensionsDiv.innerHTML = '<small>No saved popup configurations yet</small>';
                return;
            }

            let dimensionsHTML = '<small><strong>Currently Saved Configurations:</strong></small><br>';

            for (const [urlKey, config] of Object.entries(popupConfigs)) {
                const displayName = urlKey === 'weblink' ? 'eLeadCRM Weblink' : urlKey;
                dimensionsHTML += `
                    <div class="dimension-info">
                        <span>${displayName}:</span>
                        <span>${config.width}×${config.height} at (${config.left}, ${config.top})</span>
                    </div>
                `;
            }

            currentDimensionsDiv.innerHTML = dimensionsHTML;
        } catch (error) {
            console.error('Error loading popup dimensions:', error);
        }
    }

    async updateDefaultPopupConfig() {
        try {
            // Update the default configuration used by Tab to Popup converter
            const defaultConfig = {
                width: this.settings.popupWidth,
                height: this.settings.popupHeight,
                left: Math.round((screen.width - this.settings.popupWidth) / 2),
                top: Math.round((screen.height - this.settings.popupHeight) / 2)
                // alwaysOnTop removed - Chrome extension limitations
            };

            // Send message to content scripts to update default config
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'updateDefaultPopupConfig',
                    config: defaultConfig
                });
            }
        } catch (error) {
            console.error('Error updating default popup config:', error);
        }
    }

    updateUI() {
        // Any additional UI updates can go here
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.trueBDCPopup = new TrueBDCPopup();
});
